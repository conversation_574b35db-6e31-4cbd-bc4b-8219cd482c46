
// REPLACE THIS WITH YOUR ACTUAL AUTH TOKEN
const AUTH_TOKEN = 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6ImVmMjQ4ZjQyZjc0YWUwZjk0OTIwYWY5YTlhMDEzMTdlZjJkMzVmZTEiLCJ0eXAiOiJKV1QifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TWwzSROBJxiXGOB-9EB_Qc8mK_D8bRs5L9DuSl6AgDhdnFa6YJzlBeThC_YZsMeQlqzTyLqOdhfivABC7u54WdewaivPNozR7IUuElSE8vz9a7lvhDMDo3nRRaf7CKa77AeALkNr-5ePaOg0hHP0cpLYY5rhTATrC7rlq_xEXKKJ2TEnm0vN0tbWw_rGB3a6Oa2JglpAR-IREJxEvxyI610--QlIYeIZryWnx5aDNTChKGWgr6bOYFvgCapFTKHWD4B7jvmkpto5DBjna0mfwi1lPpLP4xvRXbFlUvEuwwBjN319jwiJUAJYXHSg51GqxuzzE3qn9OZ62lp0BEpxMw';
const BACKEND_URL = 'http://localhost:5351';

(function($) {
    'use strict';

    $(document).ready(function() {
        if (isAppUserEditPage()) {
            const userId = getUserIdFromPage();
            if (userId) {
                console.log('Adding credit management for user ID:', userId);
                addCreditManagementSection(userId);
            }
        }
    });

})(jQuery);

function getUserIdFromPage() {
    const urlParams = new URLSearchParams(window.location.search);
    const postId = urlParams.get('post');
    const action = urlParams.get('action');
    
    if (postId && action === 'edit') {
        return postId;
    }
    
    return null;
}

function isAppUserEditPage() {
    
    const pageTitle = document.title.toLowerCase();
    const heading = document.querySelector('.wrap h1');
    
    if (pageTitle.includes('app_user') || 
        (heading && heading.textContent.toLowerCase().includes('app_user'))) {
        return true;
    }
    return false;
}

function addCreditManagementSection(userId) {
    const $ = jQuery;

    const creditSection = `
        <div id="credit-management-section">
            <div class="postbox-header">
                <h2>Credit Management</h2>
            </div>
            <div class="inside">
                <div class="credit-info-grid">
                    <div class="credit-balance-display">
                        <h3>Current Balance</h3>
                        <p class="credit-balance-number" id="current-balance">Loading...</p>
                    </div>

                    <div class="credit-adjust-form">
                        <h4>Adjust Credit</h4>
                        <input type="number" id="credit-amount" placeholder="Amount (+/-)" step="1" min="-9999" max="9999" />
                        <input type="text" id="credit-reason" placeholder="Reason for adjustment" maxlength="200" />
                        <button id="adjust-credit-btn" class="button button-primary" disabled>
                            <span class="dashicons dashicons-plus-alt2"></span> Adjust Credit
                        </button>
                    </div>
                </div>

                <div id="credit-messages"></div>

                <div class="credit-history">
                    <h4>Recent Transactions</h4>
                    <div id="transaction-list" class="loading">Loading transactions...</div>
                </div>
            </div>
        </div>
    `;

    let insertTarget = null;

    insertTarget = $('.wrap h1').first();
    if (insertTarget.length) {
        insertTarget.after(creditSection);
    } else {
        insertTarget = $('#post-body-content');
        if (insertTarget.length) {
            insertTarget.before(creditSection);
        } else {
            insertTarget = $('#poststuff');
            if (insertTarget.length) {
                insertTarget.prepend(creditSection);
            } else {
                $('.wrap').append(creditSection);
            }
        }
    }

    loadCreditData(userId);

    $('#adjust-credit-btn').click(() => adjustCredit(userId));
    $('#credit-amount, #credit-reason').on('input', validateAdjustForm);

    $('#credit-amount').focus();
}

async function loadCreditData(userId) {
    const $ = jQuery;

    try {
        showMessage('Loading credit information...', 'info');

        const response = await fetch(`${BACKEND_URL}/api/admin/credit/user-info?userId=${userId}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': AUTH_TOKEN
            },
            credentials: 'same-origin'
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.success) {
            $('#current-balance').text(result.data.balance);
            displayTransactionHistory(result.data.history);
            clearMessages();
        } else {
            showMessage('Error loading credit data: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('Error loading credit data:', error);
        showMessage('Error loading credit data. Check console for details.', 'error');

        $('#current-balance').text('Error');
        $('#transaction-list').html('<p>Unable to load transaction history. Please refresh the page.</p>');
    }
}

async function adjustCredit(userId) {
    const $ = jQuery;

    const amount = parseInt($('#credit-amount').val());
    const reason = $('#credit-reason').val().trim();

    if (!amount || !reason) {
        showMessage('Please enter both amount and reason', 'error');
        return;
    }

    if (amount === 0) {
        showMessage('Amount cannot be zero', 'error');
        return;
    }

    if (Math.abs(amount) > 1000) {
        if (!confirm(`You are adjusting ${Math.abs(amount)} credits. This is a large amount. Are you sure?`)) {
            return;
        }
    }

    try {
        const $button = $('#adjust-credit-btn');
        $button.prop('disabled', true).html('<span class="dashicons dashicons-update spin"></span> Processing...');

        const response = await fetch(`${BACKEND_URL}/api/admin/credit/adjust`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': AUTH_TOKEN
            },
            credentials: 'same-origin',
            body: JSON.stringify({
                userId: userId,
                amount: amount,
                description: reason
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.success) {
            const newBalance = result.data.newBalance;
            showMessage(`Credit adjusted successfully! New balance: ${newBalance} credits`, 'success');

            $('#credit-amount').val('');
            $('#credit-reason').val('');

            $('#current-balance').text(newBalance);

            setTimeout(() => loadCreditData(userId), 1000);
        } else {
            showMessage('Error adjusting credit: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('Error adjusting credit:', error);
        showMessage('Error adjusting credit. Check console for details.', 'error');
    } finally {
        $('#adjust-credit-btn')
            .prop('disabled', false)
            .html('<span class="dashicons dashicons-plus-alt2"></span> Adjust Credit');
    }
}

function displayTransactionHistory(transactions) {
    const $ = jQuery;

    if (!transactions || transactions.length === 0) {
        $('#transaction-list').html('<p style="text-align: center; color: #646970; font-style: italic;">No transactions found</p>');
        return;
    }

    const html = transactions.map(t => {
        const amount = parseInt(t.amount || 0);
        const amountClass = amount > 0 ? 'positive' : 'negative';
        const amountText = amount > 0 ? `+${amount}` : amount.toString();
        const icon = amount > 0 ? '⬆' : '⬇';

        const date = new Date(t.post_date);
        const formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

        return `
            <div class="transaction-item">
                <div class="transaction-details">
                    <div class="transaction-type">
                        ${icon} ${t.transaction_type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </div>
                    <div class="transaction-meta">
                        ${t.description || 'No description'}<br>
                        📅 ${formattedDate}
                        ${t.created_by ? ` | 👤 ${t.created_by}` : ''}
                        ${t.reference_id ? ` | 🔗 Ref: ${t.reference_id}` : ''}
                    </div>
                </div>
                <div class="transaction-amount ${amountClass}">
                    ${amountText} credits
                </div>
            </div>
        `;
    }).join('');

    $('#transaction-list').html(html);
}

function validateAdjustForm() {
    const $ = jQuery;

    const amount = $('#credit-amount').val();
    const reason = $('#credit-reason').val().trim();

    const isValid = amount && reason && amount !== '0' && amount !== '';
    $('#adjust-credit-btn').prop('disabled', !isValid);
}

function showMessage(message, type = 'info') {
    const $ = jQuery;

    const messageClass = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
    $('#credit-messages').html(`<div class="${messageClass}">${message}</div>`);

    if (type === 'success') {
        setTimeout(() => {
            $('#credit-messages .success').fadeOut();
        }, 5000);
    }
}

function clearMessages() {
    const $ = jQuery;
    $('#credit-messages').empty();
}

console.log('Credit Management Script Loaded');
console.log('Current URL:', window.location.href);
console.log('Is App User Edit Page:', isAppUserEditPage());
console.log('Auth Token Set:', AUTH_TOKEN !== 'Bearer YOUR_AUTH_TOKEN_HERE');
