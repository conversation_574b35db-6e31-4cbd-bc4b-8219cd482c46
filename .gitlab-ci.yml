stages:
  - build
  - deploy

build backend production:
  stage: build
  tags:
    - shell
  only:
    - master
  script:
    - nvm install 18.18.0 && nvm use 18.18.0
    - npm i -g yarn vercel
    - cd backend
    - yarn install
    - vercel build --prod
  artifacts:
    paths:
      - backend/.next
      - backend/.vercel/output
    expire_in: 1 day

build backend preview:
  stage: build
  tags:
    - shell
  except:
    - master
  script:
    - nvm install 18.18.0 && nvm use 18.18.0
    - npm i -g yarn vercel
    - cd backend
    - yarn install
    - vercel build
  artifacts:
    paths:
      - backend/.next
      - backend/.vercel/output
    expire_in: 1 day

deploy backend to preview:
  stage: deploy
  tags:
    - shell
  except:
    - master
  script:
    - nvm install 18.18.0 && nvm use 18.18.0
    - npm i -g yarn vercel
    - cd backend
    - yarn install
    - URL=$(vercel deploy --prebuilt --skip-domain --archive=tgz --token=$VERCEL)
    - echo "DEPLOY DONE"
    - echo $URL
  dependencies:
    - build backend preview

deploy backed to production:
  stage: deploy
  tags:
    - shell
  only:
    - master
  script:
    - nvm install 18.18.0 && nvm use 18.18.0
    - npm i -g yarn vercel
    - cd backend
    - yarn install
    - URL=$(vercel deploy --prebuilt --prod --archive=tgz --token=$VERCEL)
    - echo "DEPLOY DONE"
    - echo $URL
  dependencies:
    - build backend production
