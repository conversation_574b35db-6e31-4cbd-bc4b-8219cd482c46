stages:
    - upload

upload to preview:
    stage: upload
    # image: node:16
    tags:
        - shell
    except:
        - main
    script:
        - nvm install 16 && nvm use 16
        - npm i -g yarn vercel
        # - vercel pull --yes --environment=preview --token=$VERCEL
        # - vercel build --token=$VERCEL
        # - vercel deploy --prebuilt  --token=$VERCEL
        - vercel deploy --token=$VERCEL

upload to production:
  stage: upload
  # image: node:16
  tags:
    - shell
  only:
    - main
  script:
    - nvm install 16 && nvm use 16
    - npm i -g yarn vercel
    # - yarn add pnpm@8.15.7
    # - yarn global add vercel
    # - vercel pull --yes --environment=production --token=$VERCEL
    # - vercel build --prod --token=$VERCEL
    # - vercel deploy --prebuilt --prod --token=$VERCEL
    - vercel deploy --prod --token=$VERCEL
