/**
 * Sync review summary to user table when reviews change
 * This eliminates N+1 queries in jobs API
 */

import { getWPFilterORM, putWP } from "./callWP.js";

/**
 * Update review summary for a specific user
 * @param {string|number} userId - The user ID to update review summary for
 * @returns {Promise<boolean>} - Success status
 */
export const updateUserReviewSummary = async (userId) => {
  try {
    console.log(`Updating review summary for user ${userId}...`);
    
    // Get all reviews for this user
    const reviews = await getWPFilterORM(`/reviews`, { 
      review_recipient: userId 
    }, "rate_number");
    
    if (!reviews) {
      console.log(`No reviews found for user ${userId}`);
      return false;
    }
    
    // Calculate summary statistics
    const totalReviews = Array.isArray(reviews) ? reviews.length : 0;
    let averageRating = 0;
    
    if (totalReviews > 0) {
      const validRatings = reviews
        .map(review => parseFloat(review.rate_number || 0))
        .filter(rating => !isNaN(rating) && rating > 0);
      
      if (validRatings.length > 0) {
        const sum = validRatings.reduce((acc, rating) => acc + rating, 0);
        averageRating = Math.round((sum / validRatings.length) * 10) / 10; // Round to 1 decimal
      }
    }
    
    // Create review summary object
    const reviewSummary = {
      total_reviews: totalReviews,
      average_rating: averageRating,
      last_updated: new Date().toISOString()
    };
    
    // Update user table with summary
    const updateResult = await putWP(`/app_user/${userId}`, {
      review_summary: JSON.stringify(reviewSummary)
    });
    
    if (updateResult && !updateResult.code) {
      console.log(`✅ Updated review summary for user ${userId}: ${averageRating} avg (${totalReviews} reviews)`);
      return true;
    } else {
      console.error(`❌ Failed to update user ${userId}:`, updateResult);
      return false;
    }
    
  } catch (error) {
    console.error(`❌ Error updating review summary for user ${userId}:`, error);
    return false;
  }
};

/**
 * Batch update review summaries for multiple users
 * @param {Array<string|number>} userIds - Array of user IDs
 * @returns {Promise<{success: number, failed: number}>} - Update results
 */
export const batchUpdateReviewSummaries = async (userIds) => {
  console.log(`Batch updating review summaries for ${userIds.length} users...`);
  
  let successCount = 0;
  let failedCount = 0;
  
  // Process in batches to avoid overwhelming the server
  const batchSize = 5;
  for (let i = 0; i < userIds.length; i += batchSize) {
    const batch = userIds.slice(i, i + batchSize);
    
    const results = await Promise.allSettled(
      batch.map(userId => updateUserReviewSummary(userId))
    );
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value === true) {
        successCount++;
      } else {
        failedCount++;
        console.error(`Failed to update user ${batch[index]}:`, result.reason);
      }
    });
    
    // Small delay between batches
    if (i + batchSize < userIds.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  console.log(`✅ Batch update completed: ${successCount} success, ${failedCount} failed`);
  return { success: successCount, failed: failedCount };
};

/**
 * Migration function to populate review summaries for all existing users
 * @returns {Promise<{success: number, failed: number}>} - Migration results
 */
export const migrateAllUserReviewSummaries = async () => {
  try {
    console.log('🚀 Starting migration: Populating review summaries for all users...');
    
    // Get all users
    const allUsers = await getWPFilterORM('/app_user', {}, 'id');
    
    if (!allUsers || !Array.isArray(allUsers)) {
      console.error('❌ Failed to get users for migration');
      return { success: 0, failed: 0 };
    }
    
    console.log(`Found ${allUsers.length} users to migrate`);
    
    const userIds = allUsers.map(user => user.id);
    const results = await batchUpdateReviewSummaries(userIds);
    
    console.log('🎉 Migration completed!', results);
    return results;
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    return { success: 0, failed: 0 };
  }
};

/**
 * Get review summary for a user (with fallback to real-time calculation)
 * @param {string|number} userId - User ID
 * @returns {Promise<{total_reviews: number, average_rating: number}>} - Review summary
 */
export const getUserReviewSummary = async (userId) => {
  try {
    // Try to get cached summary first
    const user = await getWPFilterORM(`/app_user`, { id: userId }, 'review_summary');
    
    if (user && user[0] && user[0].review_summary) {
      try {
        const summary = JSON.parse(user[0].review_summary);
        if (summary.total_reviews !== undefined && summary.average_rating !== undefined) {
          return {
            total_reviews: summary.total_reviews,
            average_rating: summary.average_rating
          };
        }
      } catch (parseError) {
        console.warn(`Failed to parse review_summary for user ${userId}, falling back to real-time calculation`);
      }
    }
    
    // Fallback: calculate in real-time
    const reviews = await getWPFilterORM(`/reviews`, { review_recipient: userId }, "rate_number");
    const totalReviews = Array.isArray(reviews) ? reviews.length : 0;
    let averageRating = 0;
    
    if (totalReviews > 0) {
      const validRatings = reviews
        .map(review => parseFloat(review.rate_number || 0))
        .filter(rating => !isNaN(rating) && rating > 0);
      
      if (validRatings.length > 0) {
        const sum = validRatings.reduce((acc, rating) => acc + rating, 0);
        averageRating = Math.round((sum / validRatings.length) * 10) / 10;
      }
    }
    
    return { total_reviews: totalReviews, average_rating: averageRating };
    
  } catch (error) {
    console.error(`Error getting review summary for user ${userId}:`, error);
    return { total_reviews: 0, average_rating: 0 };
  }
};
