import { getWPFilterORM } from "./callWP.js";

export const checkAdminPermission = async (user) => {
  try {
    if (!user || !user.email) {
      return false;
    }
    
    const adminUser = await getWPFilterORM('/app_user', { email: user.email }, 'user_type,id');
    
    // Check if user exists and has admin type
    if (!adminUser[0]) {
      return false;
    }
    
    // You can modify this logic based on your admin system
    // For now, checking if user_type is 'Admin'
    return adminUser[0]?.user_type === 'Admin';
    
  } catch (error) {
    console.error('Error checking admin permission:', error);
    return false;
  }
};

export const requireAdmin = async (req, res, user) => {
  const isAdmin = await checkAdminPermission(user);
  
  if (!isAdmin) {
    res.status(403).json({ 
      success: false, 
      message: "Admin access required. Please contact administrator if you believe this is an error." 
    });
    return false;
  }
  
  return true;
};
