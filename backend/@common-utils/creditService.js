import { getWPFilterORM, postWP } from './callWP.js';

const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);

export const addCreditTransaction = async (userId, amount, type, description, referenceId = null, createdBy = 'system') => {
  try {
    // Get user's Stripe customer ID
    const user = await getWPFilterORM('/app_user', { id: userId }, 'business_stripe_id,user_name');
    const stripeCustomerId = user[0]?.business_stripe_id;
    
    if (!stripeCustomerId) {
      throw new Error('User does not have Stripe customer ID');
    }
    
    // Get current balance from Stripe
    const customer = await stripe.customers.retrieve(stripeCustomerId);
    const currentBalance = parseInt(customer.metadata.credits || 0);
    const newBalance = currentBalance + amount;
    
    // Update Stripe metadata (source of truth)
    await stripe.customers.update(stripeCustomerId, {
      metadata: {
        credits: newBalance.toString()
      }
    });
    
    // Create transaction record for audit
    const transaction = await postWP('/credit_transactions', {
      title: `${type} - ${amount} credits - ${user[0]?.user_name}`,
      status: 'publish',
      user_id: userId,
      transaction_type: type,
      amount: amount,
      balance_before: currentBalance,
      balance_after: newBalance,
      description: description,
      reference_id: referenceId,
      created_by: createdBy,
      stripe_customer_id: stripeCustomerId
    });
    
    return { success: true, newBalance, currentBalance, transaction };
  } catch (error) {
    console.error('Credit transaction failed:', error);
    throw error;
  }
};

export const getCreditBalance = async (userId) => {
  try {
    const user = await getWPFilterORM('/app_user', { id: userId }, 'business_stripe_id');
    const stripeCustomerId = user[0]?.business_stripe_id;
    
    if (!stripeCustomerId) return 0;
    
    const customer = await stripe.customers.retrieve(stripeCustomerId);
    return parseInt(customer.metadata.credits || 0);
  } catch (error) {
    console.error('Error getting credit balance:', error);
    return 0;
  }
};

export const getCreditHistory = async (userId, limit = 50) => {
  return await getWPFilterORM('/credit_transactions', 
    { user_id: userId }, 
    'id,transaction_type,amount,balance_before,balance_after,description,reference_id,created_by,post_date',
    `&per_page=${limit}&orderby=date&order=desc`
  );
};

export const auditCreditBalance = async (userId) => {
  try {
    // Get current balance from Stripe
    const stripeBalance = await getCreditBalance(userId);
    
    // Get all transactions and calculate
    const transactions = await getWPFilterORM('/credit_transactions', 
      { user_id: userId }, 
      'amount'
    );
    
    const calculatedBalance = transactions.reduce((sum, t) => sum + (t.amount || 0), 0);
    
    return {
      stripeBalance,
      calculatedBalance,
      discrepancy: stripeBalance - calculatedBalance
    };
  } catch (error) {
    console.error('Audit error:', error);
    return { error: error.message };
  }
};
