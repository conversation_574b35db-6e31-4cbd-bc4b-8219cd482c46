import { getWPFilterORM, postWP, putWP, getWP } from './callWP.js';

const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);

export const addCreditTransaction = async (userId, amount, type, description, referenceId = null, createdBy = 'system') => {
  console.log(' addCreditTransaction', userId, amount, type, description, referenceId, createdBy);
  
  try {
    const user = await getWP(`/app_user/${userId}?_fields=business_stripe_id,user_name,email`);
    let stripeCustomerId = user?.business_stripe_id;

    if (!stripeCustomerId) {
      const customer = await stripe.customers.create({
        name: user?.user_name,
        email: user?.email,
        metadata: {
          credits: '0'
        }
      });

      stripeCustomerId = customer.id;

      await putWP(`/app_user/${userId}`, {
        business_stripe_id: stripeCustomerId
      });
    }
    
    const customer = await stripe.customers.retrieve(stripeCustomerId);
    const currentBalance = parseInt(customer.metadata.credits || 0);
    const newBalance = currentBalance + amount;
    
    await stripe.customers.update(stripeCustomerId, {
      metadata: {
        credits: newBalance.toString()
      }
    });
    
    const transaction = await postWP('/credit_transactions', {
      title: `${type} - ${amount} credits - ${user?.user_name}`,
      status: 'publish',
      user_id: parseInt(userId),
      transaction_type: type,
      amount: amount,
      balance_before: currentBalance,
      balance_after: newBalance,
      description: description,
      reference_id: referenceId,
      created_by: createdBy,
      stripe_customer_id: stripeCustomerId
    });
    
    return { success: true, newBalance, currentBalance, transaction };
  } catch (error) {
    console.error('Credit transaction failed:', error);
    throw error;
  }
};

export const getCreditBalance = async (userId) => {
  try {
    const user = await getWP(`/app_user/${userId}?_fields=business_stripe_id,user_name,email`);
    console.log('user getCreditBalance', user);
    
    let stripeCustomerId = user?.business_stripe_id;

    if (!stripeCustomerId) {
      const customer = await stripe.customers.create({
        name: user?.user_name,
        email: user?.email,
        metadata: {
          credits: '0'
        }
      });

      stripeCustomerId = customer.id;

      await putWP(`/app_user/${userId}`, {
        business_stripe_id: stripeCustomerId
      });

      return 0;
    }

    const customer = await stripe.customers.retrieve(stripeCustomerId);
    return parseInt(customer.metadata.credits || 0);
  } catch (error) {
    console.error('Error getting credit balance:', error);
    return 0;
  }
};

export const getCreditHistory = async (userId, limit = 50) => {
  return await getWPFilterORM('/credit_transactions', 
    { user_id: userId }, 
    'id,transaction_type,amount,balance_before,balance_after,description,reference_id,created_by,post_date',
    `&per_page=${limit}&orderby=date&order=desc`
  );
};

export const auditCreditBalance = async (userId) => {
  try {
    const stripeBalance = await getCreditBalance(userId);
    
    const transactions = await getWPFilterORM('/credit_transactions', 
      { user_id: userId }, 
      'amount'
    );
    
    const calculatedBalance = transactions.reduce((sum, t) => sum + (t.amount || 0), 0);
    
    return {
      stripeBalance,
      calculatedBalance,
      discrepancy: stripeBalance - calculatedBalance
    };
  } catch (error) {
    console.error('Audit error:', error);
    return { error: error.message };
  }
};
