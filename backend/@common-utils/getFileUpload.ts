import fs from "fs";
const formidable = require('formidable-serverless');


type TFile = {
  path: string,
  size: number,
  name: string,
  type: string,
  mtime: string,
}

type TFileUploadObject = {
  fields: {
    [field: string]: any,
  },
  files: {
    [field: string]: TFile,
  }
}

// use this function in your api route
// for example: export const config = useConfig();
export const makeConfig = () => ({
  api: {
    bodyParser: false,
  }
})

export const getFileUpload = (req) : Promise<TFileUploadObject> => new Promise((resolve, reject) => {
  const form = new formidable.IncomingForm();
  form.parse(req, async function (err, fields, files) {
    if (err) return reject(err);
    resolve({
      fields,
      files
    });
  });
});


