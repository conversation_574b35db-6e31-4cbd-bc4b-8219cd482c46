const nodemailer = require('nodemailer');

export const sendSMTPEmail = ({ from, to, cc, subject, html, replyTo, icalEvent }: any) => new Promise((resolve, reject) => {
  let transport = nodemailer.createTransport({
    host: 'email-smtp.eu-west-1.amazonaws.com',
    port: 465,
    auth: {
      user: 'AKIAZ4VNOBJOKAJKACAH',
      pass: 'BItVDVkDU+IW3mmWPRN9zKWjOCCLV03S7ahC3mPxPY9J'
    }
  })
  const message : any = {
    from: `${from} <<EMAIL>>`,
    to,
    subject,
    html,
    replyTo,
    ...(icalEvent && { icalEvent }),
  };
  if (cc) message.cc = cc;
  transport.sendMail(message, (err, info) => {
    if (err) {
      console.log('err sendMail', err);
      return reject(err)
    } else {
      console.log(info);
      return resolve(info);
    }
  })
});

const sendPushNotification = async (email, title, contents, screen, params) => {
  try {
    if (!email) {
      console.log('Email is required for push notification');
      return;
    }

    const APP_ID = "2eb465fd-392e-46c0-9031-56b4d07bc2d7";
    const TOKEN = "Key os_v2_app_f22gl7jzfzdmbebrk22na66c242mvdlmhytekpnpeg33bchusikyf2dqb72p7jndb7jtdkoyyzi35ungizfca2jsl3iaze7k3enhqgi"
    const URL_NOTIFICATION = "https://onesignal.com/api/v1/notifications";

    const response = await fetch(URL_NOTIFICATION, {
      method: 'POST',
      headers: {
        "Content-Type": "application/json; charset=utf-8",
        "Authorization": TOKEN,
      },
      body: JSON.stringify({
        "app_id": APP_ID,
        "contents": {
          "en": contents || 'New notification'
        },
        "data": {
          "schemaLink": {
            "screen": screen || null,
            "params": params || null,
          },
        },
        "headings": {
          "en": title || 'Notification'
        },
        "include_external_user_ids": [email.trim()],
        "channel_for_external_user_ids": "push",
        ...(screen === "Chat" && {
          "thread_id": "chat_notifications",
          "android_group": "chat_notifications",
          "android_group_message": {"en": "You have multiple new messages"},
          "ios_relevance_score": 10,
          "collapse_id": "chat_" + params?.conversationId
        })
      }),
    });

    const result = await response.json();
    console.log('Push notification result:', result);
    return result;
  } catch (error) {
    console.error('Error sending push notification:', error);
    return null;
  }
}

type TMessageData = {
  title: string;
  text: string;
  html: string;
}

export const sendNoti = async (email, messageData: TMessageData, config = { mobile: true, mail: true }, screen ?: any, params ?: any) => {
  const { mobile, mail } = config;
  const promiseArr = [];
  if (mail) {
    promiseArr.push(
      await sendSMTPEmail({
        from: 'Trade Motion',
        to: email,
        subject: messageData.title,
        html: messageData.html,
      })
    )
    
  }
  if (mobile) {
    // SEND PUSH NOTIFICATION
    promiseArr.push(
      await sendPushNotification(email, messageData.title, messageData.text,  screen, params)
    );
  }
  await Promise.all(promiseArr);
}