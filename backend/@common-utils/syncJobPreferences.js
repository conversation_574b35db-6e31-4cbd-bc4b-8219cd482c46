
import { getWP, putWP, getWPFilterORM } from "./callWP.js";

export const syncJobFavorite = async (jobId, userId, action) => {
  try {
    const job = await getWP(`/jobs/${jobId}`);
    if (!job || job.code) {
      console.error(`Job ${jobId} not found`);
      return false;
    }

    let favoritedByUsers = [];
    if (job.favorited_by_users) {
      if (typeof job.favorited_by_users === 'string') {
        favoritedByUsers = job.favorited_by_users
          .split(',')
          .map(id => id.trim())
          .filter(id => id.length > 0);
      } else if (Array.isArray(job.favorited_by_users)) {
        favoritedByUsers = job.favorited_by_users;
      }
    }

    favoritedByUsers = favoritedByUsers.map(id => String(id));
    const userIdStr = String(userId);

    if (action === 'add') {
      if (!favoritedByUsers.includes(userIdStr)) {
        favoritedByUsers.push(userIdStr);
      }
    } else if (action === 'remove') {
      favoritedByUsers = favoritedByUsers.filter(id => id !== userIdStr);
    }

    const updateResult = await putWP(`/jobs/${jobId}`, {
      favorited_by_users: favoritedByUsers.join(',')
    });

    if (updateResult && !updateResult.code) {
      console.log(`✅ Updated job ${jobId} favorites: ${favoritedByUsers.length} users`);
      return true;
    } else {
      console.error(`Failed to update job ${jobId}:`, updateResult);
      return false;
    }

  } catch (error) {
    console.error(`Error syncing favorite for job ${jobId}:`, error);
    return false;
  }
};

export const syncJobHidden = async (jobId, userId, action) => {
  try {
    
    const job = await getWP(`/jobs/${jobId}`);
    if (!job || job.code) {
      return false;
    }

    let hiddenByUsers = [];
    if (job.hidden_by_users) {
      if (typeof job.hidden_by_users === 'string') {
        hiddenByUsers = job.hidden_by_users
          .split(',')
          .map(id => id.trim())
          .filter(id => id.length > 0);
      } else if (Array.isArray(job.hidden_by_users)) {
        hiddenByUsers = job.hidden_by_users;
      }
    }

    hiddenByUsers = hiddenByUsers.map(id => String(id));
    const userIdStr = String(userId);

    if (action === 'add') {
      if (!hiddenByUsers.includes(userIdStr)) {
        hiddenByUsers.push(userIdStr);
      }
    } else if (action === 'remove') {
      hiddenByUsers = hiddenByUsers.filter(id => id !== userIdStr);
    }

    const updateResult = await putWP(`/jobs/${jobId}`, {
      hidden_by_users: hiddenByUsers.join(',')
    });

    if (updateResult && !updateResult.code) {
      console.log(`✅ Updated job ${jobId} hidden: ${hiddenByUsers.length} users`);
      return true;
    } else {
      console.error(`Failed to update job ${jobId}:`, updateResult);
      return false;
    }

  } catch (error) {
    console.error(`Error syncing hidden for job ${jobId}:`, error);
    return false;
  }
};

export const batchSyncJobPreferences = async (jobIds, userId, action, type) => {
  
  const syncFunction = type === 'favorite' ? syncJobFavorite : syncJobHidden;
  let successCount = 0;
  let failedCount = 0;

  const batchSize = 3;
  for (let i = 0; i < jobIds.length; i += batchSize) {
    const batch = jobIds.slice(i, i + batchSize);
    
    const results = await Promise.allSettled(
      batch.map(jobId => syncFunction(jobId, userId, action))
    );
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value === true) {
        successCount++;
      } else {
        failedCount++;
        console.error(`Failed to sync ${type} for job ${batch[index]}:`, result.reason);
      }
    });
    
    if (i + batchSize < jobIds.length) {
      await new Promise(resolve => setTimeout(resolve, 50));
    }
  }

  console.log(`Batch sync completed: ${successCount} success, ${failedCount} failed`);
  return { success: successCount, failed: failedCount };
};
