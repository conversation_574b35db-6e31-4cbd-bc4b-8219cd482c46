const WP_HOST_NAME = process.env.WP_HOST_NAME;
const WP_AUTH_TOKEN = process.env.WP_AUTH_TOKEN;
const fs = require("fs");
const path = require("path");
const FormData = require("form-data");
const { execSync } = require("child_process");
// fuck fetch, axios, now i'm trying request for file upload
var request = require("request");

export const getFullWPUrl = (path) => {
  const withQuery = path.includes("?") ? true : false;
  return (
    WP_HOST_NAME +
    path +
    (withQuery
      ? "&mo_rest_api_test_config=basic_auth"
      : "?mo_rest_api_test_config=basic_auth")
  );
};

export const getWP = async (path) => {
  const url = getFullWPUrl(path);
  const response = await fetch(url, {
    method: "GET",
    headers: {
      Authorization: WP_AUTH_TOKEN,
    },
  });
  const json = await response.json();
  return json;
};

// query: { key: value }
// check /users/me.js for usage
// for complex filter, use getWP and make your own querystring
export const getWPFilterORM = async (path, query, fields) => {
  if (!query) return getWP(path);
  const basePath = path.split('?')[0];
  let filterQuery = `?per_page=100&filter[meta_query][relation]=AND`;
  
  let keys = Object.keys(query);
  for (let i = 0; i < keys.length; i++) {
    const value = query[keys[i]];
    filterQuery += `&filter[meta_query][${i}][key]=${encodeURIComponent(keys[i])}`;
    if (typeof value === 'object' && value !== null) {
      filterQuery += `&filter[meta_query][${i}][value]=${encodeURIComponent(value.value)}`;
      filterQuery += `&filter[meta_query][${i}][compare]=${value.compare || 'LIKE'}`;
    } else {
      filterQuery += `&filter[meta_query][${i}][value]=${encodeURIComponent(value)}`;
      filterQuery += `&filter[meta_query][${i}][compare]=LIKE`;
    }
  }

  const fieldsFromPath = path.match(/_fields=([^&]*)/);
  const fieldsParam = fieldsFromPath ? fieldsFromPath[1] : fields;
  const fieldsQuery = fieldsParam ? `&_fields=${fieldsParam}` : '';

  const url = getFullWPUrl(basePath + filterQuery + fieldsQuery);
  
  const response = await fetch(url, {
    method: "GET",
    headers: {
      Authorization: WP_AUTH_TOKEN,
      'Cache-Control': 'max-age=60',
    },
  });
  const json = await response.json();
  return json;
};

export const postWP = async (path, data) => {
  const url = getFullWPUrl(path);
  console.log('postWP', url, data);
  const response = await fetch(url, {
    method: "POST",
    headers: {
      Authorization: WP_AUTH_TOKEN,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });
  const json = await response.json();
  return json;
};

export const putWP = async (path, data) => {
  const url = getFullWPUrl(path);
  const response = await fetch(url, {
    method: "PUT",
    headers: {
      Authorization: WP_AUTH_TOKEN,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });
  const json = await response.json();
  return json;
};

export const deleteWP = async (path) => {
  const url = getFullWPUrl(path);
  const response = await fetch(url, {
    method: "DELETE",
    headers: {
      Authorization: WP_AUTH_TOKEN,
    },
  });
  const json = await response.json();
  return json;
};

export const uploadWP = async ({ name, filePath, contentType, size }) => {
  const url = getFullWPUrl("/media");

  var options = {
    method: "POST",
    url: url,
    headers: {
      Authorization: WP_AUTH_TOKEN,
    },
    formData: {
      file: {
        value: fs.createReadStream(filePath),
        options: {
          filename: name,
          contentType: null,
        },
      },
    },
  };
  const waitData = () =>
    new Promise((resolve, reject) => {
      // fuck fetch, axios, now i'm trying request for file upload
      request(options, function (error, response) {
        if (error) throw new Error(error);
        resolve(response.body);
      });
    });
  let textContent = await waitData();
  let json = {};
  try {
    json =
      typeof textContent === "string" ? JSON.parse(textContent) : textContent;
  } catch (error) {}
  return json;

  // using exec to run curl command
  // this work but can't install curl on vercel servers

  // const curlPath = path.join(process.cwd(), 'curl');
  // const command = `${curlPath} --location '${url}' \
  // --header 'Authorization: ${WP_AUTH_TOKEN}' \
  // --form 'file=@"${newPath}"'`;
  // console.log('command', command);
  // const stdout = execSync(command).toString();
  // // console.log('stdout', stdout);
  // // console.log('stderr', stderr);
  // let json = {};
  // try {
  //   json = JSON.parse(stdout)
  // } catch (error) {}
  // return json;
};

export const getListJobsQueryWP = async (path, query) => {
  if (!query) return getWP(path);
  const owner_id = query.owner_id;
  const trade = query.trade;
  const task = query.task;
  const firstDate = query?.firstDate;
  const secondDate = query?.secondDate;
  const job_status = query.job_status;
  const sortTimeBy = query.sortTimeBy;
  const searchRadius = query.searchRadius;

  let filterQuery = `?per_page=100&filter[meta_query][relation]=AND`;

  let user_ids = [];
  if (trade) {
    filterQuery += `&filter[meta_query][0][key]=trade`;
    filterQuery += `&filter[meta_query][0][value]=${encodeURIComponent(trade)}`;
    filterQuery += `&filter[meta_query][0][compare]=LIKE`;
  }
  if (job_status) {
    filterQuery += `&filter[meta_query][1][relation]=AND&filter[meta_query][1][0][key]=job_status`;
    filterQuery += `&filter[meta_query][1][0][value]=${encodeURIComponent(job_status)}`;
    filterQuery += `&filter[meta_query][1][0][compare]=LIKE`;
  }

  if (task) {
    // Add the task filter
    filterQuery += `&filter[meta_query][1][1][key]=task`;
    filterQuery += `&filter[meta_query][1][1][value]=${encodeURIComponent(task)}`;
    filterQuery += `&filter[meta_query][1][1][compare]=LIKE`;
  }

  if (firstDate && secondDate && firstDate !== "undefined" && secondDate !== "undefined") {
    filterQuery += `&filter[meta_query][1][key]=start_date_required`;
    filterQuery += `&filter[meta_query][1][value][]=${encodeURIComponent(firstDate)}`;
    filterQuery += `&filter[meta_query][1][value][]=${encodeURIComponent(secondDate)}`;
    filterQuery += `&filter[meta_query][1][type]=DATE`;
    filterQuery += `&filter[meta_query][1][compare]=BETWEEN`;
  }

  if (sortTimeBy === 'newest') {
    filterQuery += `&order=desc&orderby=date&meta_key=start_date_required`;
  } else if (sortTimeBy === 'oldest') {
    filterQuery += `&order=asc&orderby=date&meta_key=start_date_required`;
  }

  // let jobApplicationIds = [];
  // if (owner_id) {
  //   const jobApplicationResponse = await getWP(
  //     `/job_application?filter[meta_query][relation]=AND&filter[meta_query][0][key]=tradesperson&filter[meta_query][0][value]=${encodeURIComponent(
  //       owner_id
  //     )}&filter[meta_query][0][compare]=NOT LIKE`
  //   );
  //   jobApplicationIds = jobApplicationResponse.map(
  //     (jobApplication) => jobApplication.id
  //   );
  // }
  // console.log("jobApplicationIds", jobApplicationIds);
  // if (jobApplicationIds.length > 0) {
  //   jobApplicationIds.forEach((jobApplicationId, index) => {
  //     filterQuery += `&filter[meta_query][2][relation]=OR&filter[meta_query][2][${
  //       index + 1
  //     }][key]=application`;
  //     filterQuery += `&filter[meta_query][2][${
  //       index + 1
  //     }][value]=${encodeURIComponent(jobApplicationId)}`;
  //     filterQuery += `&filter[meta_query][2][${index + 1}][compare]=LIKE`;
  //   });
  // }
  const url = getFullWPUrl(path + filterQuery);

  const response = await fetch(url, {
    method: "GET",
    headers: {
      Authorization: WP_AUTH_TOKEN,
    },
  });
  const json = await response.json();
  return json;
};
