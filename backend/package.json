{"name": "trade-motion-backend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 5351", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@pusher/chatkit-server": "^2.4.0", "@types/node": "20.5.3", "@types/react": "18.2.21", "@types/react-dom": "18.2.7", "autoprefixer": "10.4.15", "eslint": "^9.14.0", "eslint-config-next": "13.4.19", "firebase-admin": "^12.0.0", "form-data": "^4.0.0", "formidable-serverless": "^1.1.1", "ical-generator": "4.1.0", "moment-timezone": "^0.5.45", "next": "13.4.19", "nextjs-cors": "^2.2.0", "nodemailer": "^6.9.13", "postcss": "8.4.28", "pusher": "^5.2.0", "react": "18.2.0", "react-dom": "18.2.0", "request": "^2.88.2", "stripe": "^17.2.0", "tailwindcss": "3.3.3", "typescript": "5.1.6"}, "devDependencies": {"@next/eslint-plugin-next": "^15.0.3", "eslint-plugin-react": "^7.37.2"}}