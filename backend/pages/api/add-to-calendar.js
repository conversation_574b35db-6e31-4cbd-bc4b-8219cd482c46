import ical from "ical-generator";
import { sendSMTPEmail } from "@/@common-utils/sendNoti";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import moment from 'moment-timezone';

const addToCalendar = async (req, res) => {
  const user = await checkFirebaseAuth(req);
  if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });

  const { filename, startDate, endDate, description, email, location } = req.body;

  const cal = ical({domain: 'github.com', name: 'my first iCal'});
  cal.createEvent({
    start: moment(startDate).startOf("day").format(),
    end: moment(endDate).endOf("day").format(),
    summary: description,
    description: description,
    location: location,
  });

  // Convert iCal file to string
  const icalString = cal.toString();

  // Send email with iCal attachment
  const sendMail = await sendSMTPEmail({
    from: "",
    to: email,
    subject: description,
    html: `<p>${description}</p>`,
    icalEvent: {
      filename: filename,
      method: 'request',
      content: icalString,
    }  
  });
  if (!sendMail) {
    res.json({
      success: false,
      message: "Failed to send email.",
    });
  }

  res.json({
    success: true,
    message: "Email sent successfully.",
  });
};

export default addToCalendar;
