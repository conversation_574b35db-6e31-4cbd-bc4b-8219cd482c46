const nodemailer = require('nodemailer');
import NextCors from 'nextjs-cors';

const sendSMTPEmail = ({ from, to, cc, subject, text, html, replyTo}) =>  new Promise((resolve, reject) => {
    let transport = nodemailer.createTransport({
      host: 'email-smtp.eu-west-1.amazonaws.com',
      port: 465,
      auth: {
        user: 'AKIAZ4VNOBJOKAJKACAH',
        pass: 'BItVDVkDU+IW3mmWPRN9zKWjOCCLV03S7ahC3mPxPY9J'
      }
    })
    const message = {
      from: `${from} <<EMAIL>>`,
      to,
      subject,
      html,
      replyTo
    };
    if (cc) message.cc = cc;
    transport.sendMail(message, (err, info) => {
      if(err) {
        console.log('err sendMail', err);
        return reject(err)
      } else {
        console.log(info);
        return resolve(info);
      }
    })
  })

export default async function apiSendMailTest(req, res) {
  await NextCors(req, res, {
    methods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE'],
    origin: '*',
    optionsSuccessStatus: 200, // some legacy browsers (IE11, various SmartTVs) choke on 204
  });
  // console.log('fileHTML', fileHTML);
  const { to, subject, html, from } = typeof req.body === 'string' ? JSON.parse(req.body) : req.body;
  const result = await sendSMTPEmail({
    from,
    to,
    subject,
    html: html,
  });
  res.json({ success: true, data: result });
}