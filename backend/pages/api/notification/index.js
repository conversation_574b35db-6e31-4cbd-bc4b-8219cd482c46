import { allowCORS } from "@/@common-utils/allowCORS";
import { sendNoti } from "@/@common-utils/sendNoti";

const sendNotification = async (req, res) => {
  await allowCORS(req, res);
  if (req.method === "POST") {
    const { email, messageData, config = { mobile: true, mail: true }, screen, params} = req.body;
    if (!email || !messageData) {
      res.json({
        success: false,
        message: "Missing required fields.",
      });
      return;
    }
    try {
      await sendNoti(email, messageData, config,  screen, params);
      res.json({
        success: true,
        message: "Notification sent.",
      });
    } catch (error) {
      console.log('sendNotification error', error);
      res.json({
        success: false,
        message: "Failed to send notification.",
      });
    }
  }
};

export default sendNotification;
