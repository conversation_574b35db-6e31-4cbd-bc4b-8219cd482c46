import { allowCORS } from "@/@common-utils/allowCORS";
import { getWPFilterORM } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

export const HomeScreenAPI = async (req, res) => {
	try {
		await allowCORS(req, res);

		const user = await checkFirebaseAuth(req);
		if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });

		if (req.method === "GET") {
			let thisUser = await getWPFilterORM(`/app_user?_fields=id,user_type`, {
				Email: user.email,
			});

			const userType = thisUser[0].user_type[0];

			// message
			const [conversations, applicationReadStatus] = await Promise.all([
				getWPFilterORM(`/conversation?_fields=unique_email_id,read_status_user_1,read_status_user_2`, { 
					user_emails: user.email 
				}),
				getWPFilterORM(`/is_read_application?_fields=job_application_id,read_timestamp`, {
					user_id: user.user_id,
				})
			]);
			let newConversationNumber = 0;

			if (!conversations || "message" in conversations || !Array.isArray(conversations)) {
				res.json({
					success: false,
					message: `Failed to get conversation. ` + conversations?.message,
				});
				return;
			}

			conversations.forEach((item) => {
				const userIndex = item.unique_email_id.indexOf(user.email) === 0 ? 0 : 1;

				if (userIndex === 0 && (!item.read_status_user_1 || item.read_status_user_1.includes("unread"))) {
					newConversationNumber++;
				}

				if (userIndex === 1 && (!item.read_status_user_2 || item.read_status_user_2.includes("unread"))) {
					newConversationNumber++;
				}
			});

			// news
			let unreadNewsNumber = 0;
			let allReadId=[];
			let unreadFlash = [];
			let allFlashId = [];
			if (userType === "Tradesperson") {
				const [readStatus, allFlash] = await Promise.all([
					getWPFilterORM(`/is_read_flash?_fields=flash_id`, {
						user_id: thisUser[0].id,
					}),
					getWPFilterORM(`/flash?_fields=id`)
				]);

				if (!readStatus || "message" in readStatus) {
					res.json({
						success: false,
						message: `Failed to get readStatus. ` + readStatus?.message,
					});
					return;
				}

				if (!allFlash || "message" in allFlash || !Array.isArray(allFlash)) {
					res.json({
						success: false,
						message: `Failed to get allFlash. ` + allFlash?.message,
					});
					return;
				}

				allFlashId = allFlash.map((item) => item.id);
				allReadId = readStatus.map((item) => item.flash_id);

				unreadFlash = allFlashId.filter((id) => !allReadId.includes(id.toString()));
				unreadNewsNumber = unreadFlash.length;
			}

			// review
			const reviews = await getWPFilterORM(
				`/reviews`,
				{
					review_recipient: thisUser[0].id,
				},
				"review_read_status"
			);

			const unreadReviews = reviews?.filter((item) => !item.review_read_status.includes("read"));

			// application/current job
			let jobApplication;
			let filteredReadStatus = [];
			let filteredApplications = [];
			let filteredCurrentJobs = [];
			if (Array.isArray(applicationReadStatus)) {
				filteredReadStatus = applicationReadStatus
					.filter((item) => item.read_timestamp)
					.map((item) => item.job_application_id);

				if (userType === "Tradesperson") {
					jobApplication = await getWPFilterORM(
						`/job_application?_fields=id,job_status`,
						{
							tradesperson_email: user.email,
							job_status: "Approved",
						}
					);

					if (!jobApplication || "message" in jobApplication) {
						res.json({
							success: false,
							message: `Failed to get jobApplication. ` + jobApplication?.message,
						});
						return;
					}

					filteredApplications = jobApplication.filter(
						(job) => job.job_status.includes("Approved") && !filteredReadStatus.includes(job.id)
					);
				} else {
					jobApplication = await getWPFilterORM(
						`/job_application?_fields=id,job_status`,
						{
							business_email: user.email,
							job_status: "Pending",
						}
					);

					if (!jobApplication || "message" in jobApplication) {
						res.json({
							success: false,
							message: `Failed to get jobApplication. ` + jobApplication?.message,
						});
						return;
					}

					filteredCurrentJobs = jobApplication.filter(
						(job) => job.job_status.includes("Pending") && !filteredReadStatus.includes(job.id)
					);
				}
			}
			console.log('homeScreen', {
				messages: newConversationNumber,
				jobApplication: filteredApplications.length,
				currentJobs: filteredCurrentJobs.length,
				profile: unreadReviews.length,
				news: unreadNewsNumber,
				reviews
			});
			res.json({
				success: true,
				data: {
					// filteredCurrentJobs,
					// jobApplications: jobApplication,
					// conversations,
					// unreadFlash,
					// thisUser,
					// readStatus,
					// allFlash,
					// allFlashId,
					// allReadId,
					messages: newConversationNumber,
					jobApplication: filteredApplications.length,
					currentJobs: filteredCurrentJobs.length,
					profile: unreadReviews.length,
					news: unreadNewsNumber,
					reviews
				},
			});
		}
	} catch (error) {
		console.log("error", error);
		res.status(500).json({ success: false, message: "Internal server error" });
	}
};

export default HomeScreenAPI;
