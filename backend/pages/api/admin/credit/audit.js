import { allowCORS } from "@/@common-utils/allowCORS";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { auditCreditBalance } from "@/@common-utils/creditService";
import { getWP } from "@/@common-utils/callWP";

const auditCredit = async (req, res) => {
  try {
    await allowCORS(req, res);
    if (req.method !== "GET") {
      return res.status(405).json({ success: false, message: "Method not allowed" });
    }
    
    const user = await checkFirebaseAuth(req);
    if (!user) {
      return res.status(403).json({ success: false, message: "Unauthorized" });
    }
    
    // User is authenticated via Firebase, no additional admin check needed
    
    const { userId } = req.query;
    
    if (!userId) {
      return res.status(400).json({ success: false, message: "User ID required" });
    }
    
    // Check if target user exists
    const targetUser = await getWP(`/app_user/${userId}?_fields=id,user_name,email`);
    if (!targetUser || targetUser.code) {
      return res.status(404).json({ success: false, message: "User not found" });
    }
    
    const auditResult = await auditCreditBalance(userId);
    
    res.json({ 
      success: true, 
      data: {
        ...auditResult,
        user: targetUser,
        hasDiscrepancy: auditResult.discrepancy !== 0
      }
    });
  } catch (error) {
    console.error('Credit audit error:', error);
    res.status(500).json({ success: false, message: error.message });
  }
};

export default auditCredit;
