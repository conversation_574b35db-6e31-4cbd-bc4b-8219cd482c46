import { allowCORS } from "@/@common-utils/allowCORS";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

const generateAuth = async (req, res) => {
  try {
    await allowCORS(req, res);
    if (req.method !== "GET") {
      return res.status(405).json({ success: false, message: "Method not allowed" });
    }
    
    const user = await checkFirebaseAuth(req);
    if (!user) {
      return res.status(403).json({ success: false, message: "Unauthorized" });
    }
    
    // Extract the authorization header
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.status(400).json({ 
        success: false, 
        message: "No authorization header found" 
      });
    }
    
    // Return the token for use in code snippets
    res.json({
      success: true,
      data: {
        token: authHeader,
        user: {
          email: user.email,
          uid: user.uid
        },
        usage: {
          description: "Use this token in WordPress admin code snippets",
          example: `
// In your JavaScript code snippet:
const authToken = '${authHeader}';

fetch('/wp-json/api/admin/credit/user-info?userId=123', {
  headers: {
    'Authorization': authToken,
    'Content-Type': 'application/json'
  }
});
          `
        }
      }
    });
  } catch (error) {
    console.error('Generate auth error:', error);
    res.status(500).json({ success: false, message: error.message });
  }
};

export default generateAuth;
