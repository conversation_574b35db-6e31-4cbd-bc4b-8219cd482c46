import { allowCORS } from "@/@common-utils/allowCORS";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { addCreditTransaction, getCreditBalance, getCreditHistory, auditCreditBalance } from "@/@common-utils/creditService";
import { getWPFilterORM } from "@/@common-utils/callWP";

const testCreditSystem = async (req, res) => {
  try {
    await allowCORS(req, res);
    
    const user = await checkFirebaseAuth(req);
    if (!user) {
      return res.status(403).json({ success: false, message: "Unauthorized" });
    }
    
    // User is authenticated via Firebase, no additional admin check needed

    if (req.method === "GET") {
      // Test endpoint to check system status
      const { userId } = req.query;
      
      if (!userId) {
        return res.json({
          success: true,
          message: "Credit system test endpoint",
          endpoints: {
            "GET /api/admin/credit/test?userId=123": "Get user credit info",
            "POST /api/admin/credit/test": "Create test transaction",
            "PUT /api/admin/credit/test": "Test audit function"
          }
        });
      }
      
      // Get user credit info
      const [balance, history, audit] = await Promise.all([
        getCreditBalance(userId),
        getCreditHistory(userId, 5),
        auditCreditBalance(userId)
      ]);
      
      return res.json({
        success: true,
        data: {
          userId,
          balance,
          history,
          audit
        }
      });
    }
    
    if (req.method === "POST") {
      // Create test transaction
      const { userId, amount = 10, description = "Test transaction" } = req.body;
      
      if (!userId) {
        return res.status(400).json({ success: false, message: "userId required" });
      }
      
      const result = await addCreditTransaction(
        userId,
        amount,
        'admin_adjustment',
        description,
        'test-' + Date.now(),
        `test:${user.email}`
      );
      
      return res.json({
        success: true,
        message: "Test transaction created",
        data: result
      });
    }
    
    if (req.method === "PUT") {
      // Test audit function
      const { userId } = req.body;
      
      if (!userId) {
        return res.status(400).json({ success: false, message: "userId required" });
      }
      
      const audit = await auditCreditBalance(userId);
      
      return res.json({
        success: true,
        message: "Audit completed",
        data: audit
      });
    }
    
    return res.status(405).json({ success: false, message: "Method not allowed" });
    
  } catch (error) {
    console.error('Credit test error:', error);
    res.status(500).json({ success: false, message: error.message, stack: error.stack });
  }
};

export default testCreditSystem;
