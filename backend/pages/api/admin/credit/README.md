# Credit Management API Documentation

## Overview
This API system manages user credits stored in Stripe metadata with transaction tracking in WordPress for audit purposes.

## Prerequisites
1. WordPress Pods setup with `credit_transaction` custom post type
2. Stripe integration configured
3. Firebase authentication token for API access

## API Endpoints

### 1. Admin Credit Adjustment
**POST** `/api/admin/credit/adjust`

Allows admin to add or remove credits from a user account.

**Request Body:**
```json
{
  "userId": "123",
  "amount": 50,
  "description": "Bonus credits for loyal customer"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "newBalance": 150,
    "currentBalance": 100,
    "transaction": { ... }
  }
}
```

### 2. Get User Credit Info
**GET** `/api/admin/credit/user-info?userId=123`

Returns user's current credit balance and recent transaction history.

**Response:**
```json
{
  "success": true,
  "data": {
    "balance": 150,
    "history": [
      {
        "id": "456",
        "transaction_type": "admin_adjustment",
        "amount": 50,
        "balance_before": 100,
        "balance_after": 150,
        "description": "Bonus credits",
        "created_by": "admin:<EMAIL>",
        "post_date": "2024-01-01T12:00:00Z"
      }
    ],
    "user": {
      "id": "123",
      "user_name": "John Doe",
      "email": "<EMAIL>"
    }
  }
}
```

### 3. Credit Audit
**GET** `/api/admin/credit/audit?userId=123`

Compares Stripe balance with calculated balance from transactions.

**Response:**
```json
{
  "success": true,
  "data": {
    "stripeBalance": 150,
    "calculatedBalance": 150,
    "discrepancy": 0,
    "hasDiscrepancy": false,
    "user": { ... }
  }
}
```

### 4. Update Credits (Enhanced for both purchase and usage)
**POST** `/api/payment/stripeUpdateDeposit`

Handles both credit purchases and usage. Creates transaction records for audit.

**Request Body for Purchase:**
```json
{
  "new_credits": 150,
  "description": "Purchased 50 credits via Stripe",
  "reference_id": "pi_1234567890"
}
```

**Request Body for Usage:**
```json
{
  "new_credits": 149,
  "description": "Posted standard job listing",
  "reference_id": "job_789"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "credits": 149,
    "previous_credits": 150,
    "change": -1
  }
}
```

### 5. Get Current Amount (Enhanced)
**GET** `/api/payment/getCurrentAmount`

Returns user's credit balance and recent history.

**Response:**
```json
{
  "success": true,
  "data": {
    "metadata": {
      "credits": 149
    },
    "balance": 149,
    "history": [ ... ]
  }
}
```

### 6. Test Endpoint (Development)
**GET/POST/PUT** `/api/admin/credit/test`

Development endpoint for testing credit system functionality.

## Transaction Types
- `purchase`: Credits bought via Stripe
- `usage`: Credits used for app features
- `admin_adjustment`: Manual adjustment by admin
- `refund`: Credit refunds

## Error Handling
All endpoints return standardized error responses:

```json
{
  "success": false,
  "message": "Error description"
}
```

Common HTTP status codes:
- `400`: Bad Request (missing/invalid parameters)
- `403`: Forbidden (not admin or unauthorized)
- `404`: Not Found (user doesn't exist)
- `405`: Method Not Allowed
- `500`: Internal Server Error

## Security
- All endpoints require Firebase authentication
- Input validation on all parameters
- Rate limiting recommended for production

## Getting Auth Token for WordPress Admin
**GET** `/api/admin/credit/generate-auth`

Returns the current Firebase auth token for use in WordPress code snippets.

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "Bearer eyJhbGciOiJSUzI1NiIs...",
    "user": {
      "email": "<EMAIL>",
      "uid": "firebase_uid"
    },
    "usage": {
      "description": "Use this token in WordPress admin code snippets",
      "example": "// JavaScript code example..."
    }
  }
}
```

## WordPress Pods Setup Required

Create a Pod with these fields:
```
Pod Name: credit_transaction
Fields:
- user_id (relationship to app_user)
- transaction_type (text)
- amount (number)
- balance_before (number)
- balance_after (number)
- description (text)
- reference_id (text)
- created_by (text)
- stripe_customer_id (text)
```

## Testing
Use the test endpoint to verify system functionality:

```bash
# Get test info
GET /api/admin/credit/test

# Create test transaction
POST /api/admin/credit/test
{
  "userId": "123",
  "amount": 10,
  "description": "Test transaction"
}

# Test audit
PUT /api/admin/credit/test
{
  "userId": "123"
}
```
