import { allowCORS } from "@/@common-utils/allowCORS";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { getCreditBalance, getCreditHistory } from "@/@common-utils/creditService";
import { getWPFilterORM } from "@/@common-utils/callWP";

const getUserCreditInfo = async (req, res) => {
  try {
    await allowCORS(req, res);
    if (req.method !== "GET") {
      return res.status(405).json({ success: false, message: "Method not allowed" });
    }
    
    const user = await checkFirebaseAuth(req);
    if (!user) {
      return res.status(403).json({ success: false, message: "Unauthorized" });
    }
    
    // User is authenticated via Firebase, no additional admin check needed
    
    const { userId } = req.query;
    
    if (!userId) {
      return res.status(400).json({ success: false, message: "User ID required" });
    }
    
    // Check if target user exists
    const targetUser = await getWPFilterORM('/app_user', { id: userId }, 'id,user_name,email');
    if (!targetUser[0]) {
      return res.status(404).json({ success: false, message: "User not found" });
    }
    
    const [balance, history] = await Promise.all([
      getCreditBalance(userId),
      getCreditHistory(userId, 10)
    ]);
    
    res.json({ 
      success: true, 
      data: { 
        balance, 
        history,
        user: targetUser[0]
      } 
    });
  } catch (error) {
    console.error('Get user credit info error:', error);
    res.status(500).json({ success: false, message: error.message });
  }
};

export default getUserCreditInfo;
