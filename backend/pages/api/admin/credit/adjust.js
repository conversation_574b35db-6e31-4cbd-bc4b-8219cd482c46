import { allowCORS } from "@/@common-utils/allowCORS";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { addCreditTransaction } from "@/@common-utils/creditService";
import { getWPFilterORM } from "@/@common-utils/callWP";

const adjustCredit = async (req, res) => {
  try {
    await allowCORS(req, res);
    if (req.method !== "POST") {
      return res.status(405).json({ success: false, message: "Method not allowed" });
    }
    
    const user = await checkFirebaseAuth(req);
    if (!user) {
      return res.status(403).json({ success: false, message: "Unauthorized" });
    }
    
    // User is authenticated via Firebase, no additional admin check needed
    
    const { userId, amount, description } = req.body;
    
    if (!userId || !amount || !description) {
      return res.status(400).json({ success: false, message: "Missing required fields: userId, amount, description" });
    }
    
    if (typeof amount !== 'number' || amount === 0) {
      return res.status(400).json({ success: false, message: "Amount must be a non-zero number" });
    }
    
    if (Math.abs(amount) > 10000) {
      return res.status(400).json({ success: false, message: "Amount cannot exceed 10000 credits" });
    }
    
    // Check if target user exists
    const targetUser = await getWPFilterORM('/app_user', { id: userId }, 'id,user_name');
    if (!targetUser[0]) {
      return res.status(404).json({ success: false, message: "Target user not found" });
    }
    
    const result = await addCreditTransaction(
      userId, 
      parseInt(amount), 
      'admin_adjustment', 
      description, 
      null, 
      `admin:${user.email}`
    );
    
    res.json({ success: true, data: result });
  } catch (error) {
    console.error('Admin credit adjust error:', error);
    res.status(500).json({ success: false, message: error.message });
  }
};

export default adjustCredit;
