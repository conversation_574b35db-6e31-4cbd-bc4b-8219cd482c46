import { checkFirebaseAuth } from '@/@common-utils/checkFirebaseAuth';
import { makeConfig, getFileUpload } from '@/@common-utils/getFileUpload'
import { uploadWP } from '@/@common-utils/callWP'

export const config = makeConfig();

export default async function upload(req, res) {
	// const user = await checkFirebaseAuth(req);
	const body = await getFileUpload(req);
	console.log('body', body);
	// NEED TO USE key "file" with form-data
	if (!body.files?.file) {
		res.status(200).json({ success: false, message: "No file uploaded" });
	}
	const wpUploadRes = await uploadWP({
		name:  body.files.file.name,
		filePath: body.files.file.path,
		contentType: body.files.file.type,
		size: body.files.file.size
	});
	res.status(200).json({ success: true, data: wpUploadRes });
}
