import { allowCORS } from "@/@common-utils/allowCORS";
import { getWPFilterORM } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

const login = async (req, res) => {
  console.log("LOGIN API CALLED");
  await allowCORS(req, res);
  const user = await checkFirebaseAuth(req);
  if (!user) {
    console.log("USER LOGIN FAILED");
    res.json({
      success: false,
      message: "You need to login first.",
    });
    return;
  }

  if (req.method === "POST") {
    const { email } = user;
    const findUser = await getWPFilterORM("/app_user", {
      email: email,
    });
    if (!findUser[0]) {
      res.json({
        success: false,
        message: "User not found.",
      });
      return;
    }
    if (findUser[0]?.account_status[0] === "deactive") {
      res.json({
        success: false,
        message: "Your account is deleted. Please contact support.",
      });
      return;
    } else if (findUser[0]?.account_status[0] === "pending approval") {
      res.json({
        success: false,
        message: "Your business account is pending approval. Please wait for admin verification.",
        data: findUser[0] || null,
      });
      return;
    }
    console.log("USER LOGIN SUCCESS");
    res.json({
      success: true,
      data: findUser[0] || null,
    });
  }
};

export default login;
