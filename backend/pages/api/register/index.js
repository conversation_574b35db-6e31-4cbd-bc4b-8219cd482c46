import { allowCORS } from "@/@common-utils/allowCORS";
import { postWP, getWPFilterORM } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { sendNoti } from "@/@common-utils/sendNoti";

const register = async (req, res) => {
  await allowCORS(req, res);
  const user = await checkFirebaseAuth(req);
  if (!user) {
    res.status(401).json({ error: "Unauthorized" });
    return;
  }
  if (user.email !== (req.body.email || "").toLowerCase()) {
    res.status(401).json({ error: "Unauthorized" });
    return;
  }
  if (req.method === "POST") {
    const {
      user_name,
      trade,
      postcode,
      qualifications,
      user_type,
      documents,
      company_address_1,
      company_contact,
      order_number,
      order_contact_name,
      invoicing_contact_email,
      order_documents,
      portfolio_images,
    } = req.body;
    // check if user already exists
    const existingUser = await getWPFilterORM("/app_user", {
      email: user.email,
    });
    if (existingUser.length > 0) {
      res.status(400).json({ error: "User already exists" });
      return;
    }
    try {
      const newUser = await postWP("/app_user", {
        user_name,
        email: user.email,
        ...(user_type === "Tradesperson"
          ? { 
              trade: trade, 
              qualifications, 
              documents,
              portfolio_images: portfolio_images || [],
            }
          : {
              company_address_1,
              company_contact,
              order_number,
              order_contact_name,
              invoicing_contact_email,
              order_documents,
            }),
        postcode,
        user_type,
        title: `${user_name} (${user.email})`,
        status: "publish",
        account_status:
          user_type === "Business" ? "pending approval" : "active",
      });

      // Send notification email if user type is Business
      if (!newUser) {
        res.status(400).json({ error: "Failed to register user" });
        return;
      }
      if (user_type === "Business") {
        const adminEmail = "<EMAIL>";
        const emailContent = {
          title: "New Business Account Registration",
          text: `New business account registration from ${user_name}`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
              <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <h2 style="color: #2c3e50; margin-top: 0;">New Business Account Registration</h2>
                <p style="color: #34495e;">A new business has registered and is pending approval.</p>
              </div>
              
              <div style="background-color: white; padding: 20px; border-radius: 8px; border: 1px solid #e9ecef;">
                <h3 style="color: #2c3e50; margin-top: 0;">Business Details</h3>
                <table style="width: 100%; border-collapse: collapse;">
                  <tr>
                    <td style="padding: 8px 0; color: #6c757d; width: 40%;">Business Name:</td>
                    <td style="padding: 8px 0; color: #2c3e50;"><strong>${user_name}</strong></td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; color: #6c757d;">Email:</td>
                    <td style="padding: 8px 0; color: #2c3e50;"><strong>${user.email}</strong></td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; color: #6c757d;">Company Address:</td>
                    <td style="padding: 8px 0; color: #2c3e50;"><strong>${company_address_1}</strong></td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; color: #6c757d;">Company Contact:</td>
                    <td style="padding: 8px 0; color: #2c3e50;"><strong>${company_contact}</strong></td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; color: #6c757d;">Order Number:</td>
                    <td style="padding: 8px 0; color: #2c3e50;"><strong>${order_number}</strong></td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; color: #6c757d;">Order Contact Name:</td>
                    <td style="padding: 8px 0; color: #2c3e50;"><strong>${order_contact_name}</strong></td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; color: #6c757d;">Invoicing Email:</td>
                    <td style="padding: 8px 0; color: #2c3e50;"><strong>${invoicing_contact_email}</strong></td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; color: #6c757d;">Order Documents:</td>
                    <td style="padding: 8px 0; color: #2c3e50;"><strong>${order_documents}</strong></td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; color: #6c757d;">Postcode:</td>
                    <td style="padding: 8px 0; color: #2c3e50;"><strong>${postcode}</strong></td>
                  </tr>
                </table>
              </div>
              
              <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-top: 20px;">
                <p style="color: #34495e; margin: 0;">Please review this registration and approve or reject accordingly.</p>
              </div>
            </div>
          `,
        };

        try {
          await sendNoti(adminEmail, emailContent, {
            mobile: false,
            mail: true,
          });
        } catch (error) {
          console.error("Error sending admin notification:", error);
        }

        // Return pending message for business accounts
        return res.json({
          success: false,
          message:
            "Your business account has been registered and is pending approval. We will review your details and contact you shortly.",
          data: newUser,
        });
      }
      res.json({
        success: true,
        data: newUser,
      });
    } catch (error) {
      console.error("Error registering user:", error);
      res.status(400).json({ error: "Failed to register user" });
      return;
    }
  }
};

export default register;
