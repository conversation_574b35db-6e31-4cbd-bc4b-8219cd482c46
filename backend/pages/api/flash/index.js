import { allowCORS } from "@/@common-utils/allowCORS";
import { postWP, getWPFilterORM, getWP } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { sendNoti } from "@/@common-utils/sendNoti";

export const flashApi = async (req, res) => {
  try {
    const user = await checkFirebaseAuth(req);
    if (!user)
      return res.json({
        success: false,
        code: 403,
        message: "You need to login first.",
      });
    await allowCORS(req, res);
    if (req.method === "POST") {
      const { title_flash, flash_content, flash_image, poster } = req.body;
      const flash = await postWP("/flash", {
        title_flash,
        title: title_flash,
        flash_content,
        flash_image,
        poster,
        status: "publish",
      });

      const findUserTrades = await getWPFilterORM("/app_user", {
        user_type: "Tradesperson",
      });
      if (findUserTrades) {
        const userMails = findUserTrades.map((user) => user.email);
        await userMails.forEach(async (element) => {
          try {
            await sendNoti(
              element,
              {
                title: "New Flash",
                text: `A new flash has been posted! Check it out!`,
                html: `<p>A new flash has been posted! Check it out!</p>`,
              },
              {
                mobile: true,
                mail: false,
              },
              "NewsflashDetail",
              {
                newsFlashId: flash?.id,
                isNoti: true,
              }
            );
          } catch (error) {
            console.log("sendNoti error", error);
          }
        });
      }
      if (!flash) {
        res.json({
          success: false,
          code: 500,
          message: "Failed to create flash.",
        });
        return;
      }
      res.json({
        success: true,
        data: flash,
      });
    }
    if (req.method === "GET") {
      const poster = req?.query?.poster;
      const findUser = await getWP(`/app_user${poster ? `/${poster}` : ""}`);
      if (!findUser) {
        res.json({
          success: false,
          code: 500,
          message: "Failed to get user.",
        });
        return;
      }

      const emailOfUser = findUser?.email;
      
      if (emailOfUser !== user.email) {
        res.json({
          success: false,
          code: 403,
          message: "You are not allowed to do this action",
        });
        return;
      }
      const flash = await getWPFilterORM(`/flash`, {
        ...(poster && { poster: poster }),
      });
      if (!flash) {
        res.json({
          success: false,
          code: 500,
          message: "Failed to get flash.",
        });
        return;
      }
      res.json({
        success: true,
        data: flash,
      });
    }
  } catch (error) {
    console.log("error", error);
  }
};

export default flashApi;
