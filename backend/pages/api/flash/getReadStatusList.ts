import { allowCORS } from "@/@common-utils/allowCORS";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { getWPFilterORM } from "@/@common-utils/callWP";

export const getReadFlashListApi = async (req, res) => {
  try {
    const user = await checkFirebaseAuth(req);
    if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });
    await allowCORS(req, res);

    if (req.method === "GET") {
      const userData = await getWPFilterORM(`/app_user`, { email: user.email },"id,email");

      const is_read_flash = await getWPFilterORM(`/is_read_flash`);
      
      if (!is_read_flash) {
        res.json({
          success: false,
          code: 500,
          message: "Failed to get is_read_flash.",
        });
        return;
      }

      const readId = is_read_flash.map((item) => item.flash_id);

      res.json({
        success: true,
        userData,
        // is_read_flash,
        data:readId,
      });
    }
  } catch (error) {
    console.log('error', error);
  }
};

export default getReadFlashListApi;
