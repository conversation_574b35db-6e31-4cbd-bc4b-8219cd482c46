import { checkFirebaseAuth } from '@/@common-utils/checkFirebaseAuth';
import { getWPFilterORM, postWP, putWP } from '@/@common-utils/callWP';
import { now } from "@/@common-utils/formatTime";

export default async function readFlash(req, res) {
  const user = await checkFirebaseAuth(req);
  
  if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });
  const { flash_id } = req.body;

  const userData= await getWPFilterORM(`/app_user`, { email: user.email },"id,email");
  
  const currentRecord = await getWPFilterORM(`/is_read_flash`, {
    user_id: userData[0].id,
    flash_id:flash_id,
  })

  if(currentRecord.length > 0) {
    res.status(200).json({
      success: true,
      data: "Message is already read.",
    });
  }else{
    const result = await postWP('/is_read_flash', {
      flash_id: flash_id,
      user_id: userData[0].id,
      read_timestamp: now('DD/MM/YYYY HH:mm:ss'),
      status: "publish",
      title: `${userData[0].id} has read flash${flash_id}.`,
    });

    res.status(200).json({
      success: true,
      data: {
        result,
        mess:"Message read."
      },
    });
  }
}