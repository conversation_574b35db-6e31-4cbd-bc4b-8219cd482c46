import { allowCORS } from "@/@common-utils/allowCORS";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { getWPFilterORM } from "@/@common-utils/callWP";

export const flashApi = async (req, res) => {
  try {
    const user = await checkFirebaseAuth(req);
    if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });
    await allowCORS(req, res);

    if (req.method === "GET") {
      const flash = await getWPFilterORM(`/flash`, req.query);
      if (!flash) {
        res.json({
          success: false,
          code: 500,
          message: "Failed to get flash.",
        });
        return;
      }
      res.json({
        success: true,
        data: flash,
      });
    }
  } catch (error) {
    console.log('error', error);
  }
};

export default flashApi;
