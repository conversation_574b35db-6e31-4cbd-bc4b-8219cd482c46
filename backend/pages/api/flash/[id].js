import { allowCORS } from "@/@common-utils/allowCORS";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { getWPFilterORM, putWP } from "@/@common-utils/callWP";

export default async function flashByIdApi (req, res) {
  try {
    const user = await checkFirebaseAuth(req);
    if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });
    await allowCORS(req, res);
    if (req.method === "GET") {
      const flash = await getWPFilterORM(`/flash/${req.query.id}`);
      if (!flash) {
        res.json({
          success: false,
          code: 500,
          message: "Failed to get flash.",
        });
        return;
      }
      res.json({
        success: true,
        data: flash,
      });
    }
    if (req.method === 'PUT') {
      const { title_flash, flash_content, flash_image } = req.body;
      const flash = await putWP(`/flash/${req.query.id}`, {
        title_flash,
        flash_content,
        flash_image,
      });
      if (!flash || flash.code) {
        res.json({
          success: false,
          code: 500,
          message: "Failed to update flash.",
        });
        return;
      }
      res.json({
        success: true,
        data: flash,
      });
    }
  } catch (error) {
    console.log('error', error);
  }
};
