import { allowCORS } from "@/@common-utils/allowCORS";
import { putWP } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

export const readReviewApi = async (req, res) => {
  try {
    const user = await checkFirebaseAuth(req);
    if (!user)
      return res.json({
        success: false,
        code: 403,
        message: "You need to login first.",
      });
    await allowCORS(req, res);
    
    if (req.method === "PUT") {
      console.log('req.body;', req.body);
      const { review_ids } = req.body;
  
      if (!review_ids) {
        res.json({
          success: false,
          message: "No review_ids provided.",
        });
        return;
      }

      if(!Array.isArray(review_ids)) {
        res.json({
          success: false,
          message: "Invalid review_ids provided. Must be an array.",
        })
        return;
      }


      await Promise.all(review_ids.map(
        async (review_id) => {
          await putWP('/reviews/'+review_id, {
            review_read_status: 'read',
          });
        }
      ))
    
      res.status(200).json({
        success: true,
        data: "Review read.",
      });
    }
  } catch (error) {
    console.log("error", error);
  }
};

export default readReviewApi;
