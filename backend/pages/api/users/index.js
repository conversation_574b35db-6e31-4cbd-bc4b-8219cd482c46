import { allowCORS } from "@/@common-utils/allowCORS";
import { getWPFilterORM } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

const queryUser = async (req, res) => {
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);
    if (!user) {
        res.json({
            success: false,
            code: 403,
            message: "You need to login first.",
        });
        return;
    }
    const query = req.query;
    const findUser = await getWPFilterORM("/app_user", query);
    res.json({
        success: true,
        data: findUser || null,
    });
};

export default queryUser;
