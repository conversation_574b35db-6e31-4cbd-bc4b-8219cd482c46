import { allowCORS } from "@/@common-utils/allowCORS";
import { putWP, getWP } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

const updateById = async (req, res) => {
  try {
    await allowCORS(req, res);
    const { id } = req.query;
    const user = await checkFirebaseAuth(req);
    if (!user)
      return res.json({
        success: false,
        code: 403,
        message: "You need to login first.",
      });
    if (req.method === "PUT") {
      const {
        user_name,
        email,
        trade,
        postcode,
        qualifications,
        documents,
        avatar_url,
        company_contact,
        company_address_1,
        order_number,
        order_contact_name,
        invoicing_contact_email,
        account_status,
        bio,
        portfolio_images,
        documentation,
        city,
        county,
        travel_distance,
      } = req.body;

      const app_user = await putWP(`/app_user/${id}`, {
        ...(req.body.hasOwnProperty('user_name') && { user_name }),
        ...(req.body.hasOwnProperty('email') && { email }),
        ...(req.body.hasOwnProperty('trade') && { trade }),
        ...(req.body.hasOwnProperty('postcode') && { postcode }),
        ...(req.body.hasOwnProperty('qualifications') && { qualifications }),
        ...(req.body.hasOwnProperty('avatar_url') && { avatar_url }),
        ...(req.body.hasOwnProperty('documents') && { documents }),
        ...(req.body.hasOwnProperty('company_contact') && { company_contact }),
        ...(req.body.hasOwnProperty('company_address_1') && { company_address_1 }),
        ...(req.body.hasOwnProperty('order_number') && { order_number }),
        ...(req.body.hasOwnProperty('order_contact_name') && { order_contact_name }),
        ...(req.body.hasOwnProperty('invoicing_contact_email') && { invoicing_contact_email }),
        ...(req.body.hasOwnProperty('account_status') && { account_status }),
        ...(req.body.hasOwnProperty('bio') && { bio }),
        ...(req.body.hasOwnProperty('portfolio_images') && { portfolio_images }),
        ...(req.body.hasOwnProperty('documentation') && { documentation }),
        ...(req.body.hasOwnProperty('city') && { city }),
        ...(req.body.hasOwnProperty('county') && { county }),
        ...(req.body.hasOwnProperty('travel_distance') && { travel_distance }),
      });
      res.json({
        success: true,
        data: app_user,
      });
    }
    if (req.method === "GET") {
      const app_user = await getWP(`/app_user/${id}`);
      if (!app_user) {
        res.json({
          success: false,
          code: 500,
          message: "Failed to get app_user.",
        });
        return;
      }
      res.json({
        success: true,
        data: app_user,
      });
    }
  } catch (error) {
    console.log("error", error);
  }
};

export default updateById;
