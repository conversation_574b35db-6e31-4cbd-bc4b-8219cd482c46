import { allowCORS } from "@/@common-utils/allowCORS";
import { getWPFilterORM } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

const me = async (req, res) => {
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);
    if (!user) {
        res.json({
            success: false,
            code: 403,
            message: "You need to login first.",
        });
        return;
    }
    // let filterQuery = `?filter[meta_query][relation]=AND`;
    // filterQuery += `&filter[meta_query][0][key]=email`;
    // filterQuery += `&filter[meta_query][0][value][0]=${user.email}`;
    // console.log('url', "/app_user" + filterQuery);

    const findUser = await getWPFilterORM("/app_user", {
        email: user.email,
    });
    res.json({
        success: true,
        data: findUser[0] || null,
    });
};

export default me;
