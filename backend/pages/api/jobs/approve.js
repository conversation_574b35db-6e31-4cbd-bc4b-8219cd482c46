import { allowCORS } from "@/@common-utils/allowCORS";
import { postWP, getWP, putWP, getWPFilterORM } from "@/@common-utils/callWP";
import { sendNoti } from "@/@common-utils/sendNoti";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

const approveApplication = async (req, res) => {
  try {
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);
    if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });
    if (req.method === "POST") {
      const { jobId, applicationApproveId } = req.body;
      if (!jobId || !applicationApproveId) {
        res.json({
          success: false,
          message: "jobId or applicationApproveId is required.",
        });
        return;
      }

      const jobByID = await getWP(`/jobs/${jobId}`);
      if (!jobByID) {
        res.json({
          success: false,
          code: 500,
          message: "Failed to get user.",
        });
        return;
      }

      const emailUserOfJob = jobByID?.user_post[0].email;
      if (emailUserOfJob !== user.email) {
        res.json({
          success: false,
          code: 403,
          message: "You are not allowed to do this action",
        });
        return;
      }
    
      const jobApplications = jobByID?.application;
      if (
        jobApplications.length > 0 &&
        jobApplications.some(
          (jobApplication) => jobApplication.id === applicationApproveId
        )
      ) {
        try {
          jobApplications.map(async (jobApplication) => {
            if (jobApplication.id !== applicationApproveId) {
              await putWP(`/job_application/${jobApplication.id}`, {
                job_status: "Rejected",
              });
              sendNoti(
                jobApplication.tradesperson_email,
                {
                  title: "Your application update",
                  text: `Your application has been rejected by ${jobByID?.user_post[0].user_name}`,
                  html: `<p>Your application has been rejected by ${jobByID?.user_post[0].user_name}</p>`,
                },
                {
                  mobile: true,
                  mail: false,
                }
              );
            } else {
              await putWP(`/job_application/${applicationApproveId}`, {
                job_status: "Approved",
              });
              sendNoti(
                jobApplication.tradesperson_email,
                {
                  title: "Your application update",
                  text: `Your application has been approved by ${jobByID?.user_post[0].user_name}`,
                  html: `<p>Your application has been approved by ${jobByID?.user_post[0].user_name}</p>`,
                },
                {
                  mobile: true,
                  mail: false,
                }
              );
            }
          });
        } catch (error) {
          console.log("update status error", error);
        }
        try {
          await putWP(`/jobs/${jobId}`, {
            job_status: "Closed",
          });
        } catch (error) {
          res.json({
            success: false,
            message:
              "Failed to update job status to Closed. Please try again.",
          });
        }
        res.json({
          success: true,
        });
      } else {
        res.json({
          success: false,
          message:
            "jobs is not include approvedId: " + applicationApproveId + ". ",
        });
      }
    } else {
      res.json({
        success: false,
        message:
          "jobs is not include approvedId: " + applicationApproveId + ". ",
      });
    }
  } catch (error) {
    console.log("error", error);
  }
};

export default approveApplication;
