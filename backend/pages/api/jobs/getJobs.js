import { allowCORS } from "@/@common-utils/allowCORS";
import { postWP, getWP, getListJobsQueryWP, getWPFilterORM } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

const jobsAPI = async (req, res) => {
  try {
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);
    if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });

    if (req.method === "GET") {
      // OPTIMIZED: Single parallel fetch for user and jobs
      const [currentUser, jobs] = await Promise.all([
        getWPFilterORM(`/app_user?_fields=id,email`, { email: user.email }),
        getListJobsQueryWP(`/jobs`, req.query.trade === "All" ? {...req.query, trade: undefined} : req.query)
      ]);

      if (!jobs || !currentUser || !currentUser[0]) {
        return res.json({
          success: false,
          message: "Failed to get job or user data.",
        });
      }

      const userId = currentUser[0].id;

      // OPTIMIZED: Filter jobs using denormalized fields
      const filteredJobs = jobs.filter(job => {
        // Check if job is hidden by current user using denormalized field
        const hiddenByUsers = job.hidden_by_users;
        if (hiddenByUsers) {
          if (typeof hiddenByUsers === 'string') {
            // Parse comma-separated string: "123,456,789"
            const hiddenUserIds = hiddenByUsers.split(',').map(id => id.trim());
            return !hiddenUserIds.includes(String(userId));
          } else if (Array.isArray(hiddenByUsers)) {
            return !hiddenByUsers.includes(String(userId));
          }
        }
        // If no denormalized field, job is visible (backward compatibility)
        return true;
      });

      // OPTIMIZED: Add user preferences and review data using denormalized fields
      const jobsWithUserData = filteredJobs.map(job => {
        // Get review summary from denormalized user data
        const userPost = job.user_post && job.user_post[0];
        let reviewSummary = { total_reviews: 0, average_rating: 0 };

        if (userPost && userPost.review_summary) {
          try {
            reviewSummary = JSON.parse(userPost.review_summary);
          } catch (error) {
            console.warn(`Failed to parse review_summary for user ${userPost.id}:`, error);
          }
        }

        // Check if job is favorited by current user using denormalized field
        const favoritedByUsers = job.favorited_by_users;
        let isFavorite = false;

        if (favoritedByUsers) {
          if (typeof favoritedByUsers === 'string') {
            // Parse comma-separated string: "123,456,789"
            const favoriteUserIds = favoritedByUsers.split(',').map(id => id.trim());
            isFavorite = favoriteUserIds.includes(String(userId));
          } else if (Array.isArray(favoritedByUsers)) {
            isFavorite = favoritedByUsers.includes(String(userId));
          }
        }

        return {
          ...job,
          isFavorite,
          // Keep reviews format compatible with frontend expectations
          // Frontend expects array of reviews with rate_number field
          reviews: reviewSummary.total_reviews > 0 ?
            // Create fake array to match frontend calculation logic
            Array(reviewSummary.total_reviews).fill({
              rate_number: reviewSummary.average_rating
            }) : null
        };
      });

      return res.json({
        success: true,
        data: jobsWithUserData,
        meta: {
          total_jobs: jobs.length,
          filtered_jobs: filteredJobs.length,
          user_id: userId
        }
      });
    }
  } catch (error) {
    console.log('error', error);
  }
};

export default jobsAPI;
