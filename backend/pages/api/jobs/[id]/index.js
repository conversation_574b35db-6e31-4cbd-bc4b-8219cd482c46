import { allowCORS } from "@/@common-utils/allowCORS";
import { postWP, putWP, getWP, deleteWP } from "@/@common-utils/callWP";
import { convertAddressToLatLng } from "@/@common-utils/convertAddressToLatLng";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

export default async function jobsApiById(req, res) {
  try {
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);
    if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });

    const {id} = req.query;

    console.log({query:req.query})
    
    const jobByID = await getWP(`/jobs/${id}`);
    if (!jobByID) {
      res.json({
        success: false,
        code: 500,
        message: "Failed to get job.",
      });
      return;
    }


  
    if (req.method === "GET") {
      // const job = await getWP(`/jobs/${id}`);
      // if (!job) {
      //   res.json({
      //     success: false,
      //     message: "Failed to get job.",
      //   });
      //   return;
      // }
      res.json({
        success: true,
        data: jobByID,
      });
    }
    if (req.method === "PUT") {

      const emailUserOfJob = jobByID?.user_post[0].email;

      if (emailUserOfJob !== user.email) {
        res.json({
          success: false,
          code: 403,
          message: "You are not allowed to do this action",
        });
        return;
      }

      const { start_date_required, location, price, requirements, task, job_status, user_post, short_location, trade } = req.body;
      const {location_lat: short_location_lat, location_long: short_location_long} = await convertAddressToLatLng(short_location);
      const job = await putWP(`/jobs/${id}`,{
        ...(start_date_required ? { start_date_required } : {}),
        ...(location ? {
          location,
          ...(await convertAddressToLatLng(location)),
        } : {}),
        ...(short_location ? {
          short_location,
          short_location_lat,
          short_location_long,
        } : {}),
        ...(price ? { price } : {}),
        ...(requirements ? { requirements } : {}),
        ...(task ? { task, title: task } : {}),
        ...(job_status ? { job_status } : {}),
        ...(user_post ? { user_post } : {}),
        ...(trade ? { trade } : {}),
      });
      if (!job) {
        res.json({
          success: false,
          message: "Failed to update job.",
        });
        return;
      }
      res.json({
        success: true,
        data: job,
      });
    }
    if (req.method === "DELETE") {
      const job = await deleteWP(`/jobs/${id}`);
      if (!job) {
        res.json({
          success: false,
          message: "Failed to delete job.",
        });
        return;
      }
      res.json({
        success: true,
        message: 'Job id: ' + id + ' deleted.',
      });
    }
  } catch (error) {
    console.log('error', error);
  }
};
