import { allowCORS } from "@/@common-utils/allowCORS";
import { postWP, getWP, getWPFilterORM } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

const listApplicationOfAJob = async (req, res) => {
  console.log('listApplicationOfAJob', req.query.id);
  try {
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);
    if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });

    if (req.method === "GET") {
      console.log('get applications', req.query);

      const jobByID = await getWP(`/jobs/${req.query.id}`);
      
      if (!jobByID) {
        res.json({
          success: false,
          code: 500,
          message: "Failed to get user.",
        });
        return;
      }

      const emailUserOfJob = jobByID?.user_post[0].email;
      if (emailUserOfJob !== user.email) {
        res.json({
          success: false,
          code: 403,
          message: "You are not allowed to do this action",
        });
        return;
      }
    
      const applications = await getWPFilterORM(`/job_application`, {
        job: req.query.id,
      });
      res.json({
        success: true,
        data: applications,
      });
    }
  } catch (error) {
    console.log('error', error);
  }
};

export default listApplicationOfAJob;
