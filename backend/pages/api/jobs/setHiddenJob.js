import { allowCORS } from "@/@common-utils/allowCORS";
import { getWP, getWPFilterORM } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { syncJob<PERSON><PERSON>den } from "@/@common-utils/syncJobPreferences";

/**
 * API to hide/unhide jobs using denormalized fields
 * This replaces the old user_hided_job table approach
 */
const setHiddenJobAPI = async (req, res) => {
  try {
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);
    
    if (!user) {
      return res.json({ 
        success: false, 
        code: 403, 
        message: "You need to login first." 
      });
    }

    if (req.method === "POST") {
      const { jobId, action } = req.body; // action: 'hide' or 'unhide'

      if (!jobId) {
        return res.json({
          success: false,
          code: 400,
          message: "jobId is required"
        });
      }

      // Get current user
      const currentUser = await getWPFilterORM(`/app_user?_fields=id,email`, {
        email: user.email
      });

      if (!currentUser || !currentUser[0]) {
        return res.json({ 
          success: false, 
          code: 403, 
          message: "Cannot find user on database" 
        });
      }

      const userId = currentUser[0].id;

      // Get current job to check if user already hidden it
      const job = await getWP(`/jobs/${jobId}`);
      if (!job || job.code) {
        return res.json({ 
          success: false, 
          code: 404, 
          message: "Job not found" 
        });
      }

      // Check current hidden status from denormalized field
      const hiddenByUsers = job.hidden_by_users || [];
      const currentlyHidden = Array.isArray(hiddenByUsers) 
        ? hiddenByUsers.includes(String(userId))
        : false;

      // Determine action if not specified
      let finalAction = action;
      if (!finalAction) {
        finalAction = currentlyHidden ? 'unhide' : 'hide';
      }

      // Convert action to sync function format
      const syncAction = finalAction === 'hide' ? 'add' : 'remove';
      
      // Sync denormalized field
      const success = await syncJobHidden(jobId, userId, syncAction);

      if (!success) {
        return res.json({ 
          success: false, 
          code: 500, 
          message: "Failed to update hidden status" 
        });
      }

      res.json({
        success: true,
        message: `Job ${finalAction === 'hide' ? 'hidden' : 'unhidden'} successfully`,
        data: {
          jobId,
          userId,
          action: finalAction,
          isHidden: finalAction === 'hide'
        }
      });
    }

    if (req.method === "GET") {
      // Get user's hidden jobs
      const currentUser = await getWPFilterORM(`/app_user?_fields=id,email`, {
        email: user.email
      });

      if (!currentUser || !currentUser[0]) {
        return res.json({ 
          success: false, 
          code: 403, 
          message: "Cannot find user on database" 
        });
      }

      const userId = currentUser[0].id;

      // Get all jobs where user is in hidden_by_users array
      // Note: This is for backward compatibility, normally we filter in getJobs
      const allJobs = await getWPFilterORM('/jobs', {}, 'id,hidden_by_users');
      
      const hiddenJobs = allJobs.filter(job => {
        const hiddenByUsers = job.hidden_by_users || [];
        return Array.isArray(hiddenByUsers) && hiddenByUsers.includes(String(userId));
      });

      res.json({
        success: true,
        data: hiddenJobs.map(job => job.id),
        total: hiddenJobs.length
      });
    }

  } catch (error) {
    console.error('Hidden job API error:', error);
    return res.json({
      success: false,
      code: 500,
      message: "Failed to process hidden job request",
      error: error.message
    });
  }
};

export default setHiddenJobAPI;
