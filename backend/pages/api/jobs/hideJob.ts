import { allowCORS } from "@/@common-utils/allowCORS";
import { getWPFilterORM } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { syncJobHidden } from "@/@common-utils/syncJobPreferences";

const hideJobsAPI = async (req, res) => {
  try {
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);
    if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });

    if (req.method === "POST") {
      const { jobIds } = req.body;

      if (!jobIds || !Array.isArray(jobIds) || jobIds.length === 0) {
        return res.json({
          success: false,
          code: 400,
          message: "jobIds array is required"
        });
      }

      const currentUser = await getWPFilterORM(`/app_user?_fields=id,email`, {
        email: user.email
      });

      if (!currentUser || !currentUser[0]) {
        return res.json({
          success: false,
          code: 403,
          message: "Cannot find user on database"
        });
      }

      const userId = currentUser[0].id;

      // OPTIMIZED: Use denormalized fields instead of user_hided_job table
      // Hide multiple jobs by adding user to hidden_by_users field
      let successCount = 0;
      let failedCount = 0;

      for (const jobId of jobIds) {
        try {
          const success = await syncJobHidden(jobId, userId, 'add');
          if (success) {
            successCount++;
          } else {
            failedCount++;
          }
        } catch (error) {
          console.error(`Failed to hide job ${jobId}:`, error);
          failedCount++;
        }
      }

      const results = { success: successCount, failed: failedCount };

      res.json({
        success: true,
        message: `Hidden ${results.success} jobs, ${results.failed} failed`,
        data: {
          userId,
          jobIds,
          results
        }
      });
    }
  } catch (error) {
    console.log('error', error);
  }
};

export default hideJobsAPI;
