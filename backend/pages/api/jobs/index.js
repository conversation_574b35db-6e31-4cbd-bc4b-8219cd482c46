import { allowCORS } from "@/@common-utils/allowCORS";
import { postWP, getWP, getWPFilterORM } from "@/@common-utils/callWP";
import { convertAddressToLatLng } from "@/@common-utils/convertAddressToLatLng";
import { sendNoti } from "@/@common-utils/sendNoti";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

export const jobsAPI = async (req, res) => {
  try {
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);
    if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });

    if (req.method === "POST") {
      const {
        start_date_required,
        // arrival_date_required,
        location,
        price,
        requirements,
        task,
        job_status,
        user_post,
        should_notify,
        short_location,
        trade,
        post_code,
        site_name,
        contact_name
      } = req.body;
      
      const findUser = await getWP(`/app_user${user_post ? `/${user_post}` : ""}`);

      if (!findUser) {
        res.json({
          success: false,
          code: 500,
          message: "Failed to get user.",
        });
        return;
      }

      const emailOfUser = findUser?.email;
      if (emailOfUser !== user.email) {
        res.json({
          success: false,
          code: 403,
          message: "You are not allowed to do this action",
        });
        return;
      }
    
      const {location_lat: short_location_lat, location_long: short_location_long} = await convertAddressToLatLng(short_location);
      const job = await postWP("/jobs", {
        title: task,
        start_date_required,
        // arrival_date_required,
        location,
        short_location,
        ...(!!location ? await convertAddressToLatLng(location) : {}),
        short_location_lat,
        short_location_long,
        price,
        requirements,
        task,
        job_status,
        user_post,
        status: "publish",
        trade,
        post_code,
        site_name,
        contact_name
      });
      if (!job) {
        res.json({
          success: false,
          message: "Failed to create job.",
        });
        return;
      }
      if (should_notify) {
        // send notification to user_post
        const findUserTrades = await getWPFilterORM("/app_user", {
          user_type: "Tradesperson",
        });
        if (findUserTrades) {
          const userMails = findUserTrades.map((user) => user.email);
          await userMails.forEach(async (element) => {
            try {
              await sendNoti(
                element,
                {
                  title: "New Job",
                  text: `A new job has been posted by ${job?.user_post[0].user_name}`,
                  html: `<p>A new job has been posted by ${job?.user_post[0].user_name}</p>`,
                },
                {
                  mobile: true,
                  mail: false,
                },
                "FindJob",
                {
                  jobId: job.id,
                  isNoti: true,
                }
              );
            } catch (error) {
              console.log("sendNoti error", error);
            }
          });
        }
      }
      res.json({
        success: true,
        data: job,
      });
    }
    if (req.method === "GET") {
      const filters = {
        ...req.query,
        job_status: {
          value: 'Hidden',
          compare: '!=',
        }
      };      
      const page = parseInt(req.query.page) || 1;
      const per_page = parseInt(req.query.per_page) || 20;
      
      const jobs = await getWPFilterORM(`/jobs?page=${page}&per_page=${per_page}&_fields=id,task,trade,job_status,application,location,site_name,contact_name,price,user_post,date`, filters);
      if (!jobs) {
        res.json({
          success: false,
          message: "Failed to get jobs.",
        });
        return;
      }
      res.json({
        success: true,
        data: jobs,
        pagination: {
          page,
          per_page
        }
      });
    }
  } catch (error) {
    console.log("error", error);
  }
};

export default jobsAPI;