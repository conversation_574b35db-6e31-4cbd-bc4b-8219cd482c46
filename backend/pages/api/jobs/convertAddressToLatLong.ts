

const GOOGLE_MAPS_API_KEY = process.env.GOOGLE_MAPS_API_KEY || 'AIzaSyDLy0v4v2ec7WD9ACJTqPTBb5khwR0PPeQ';

export default async function handler(req, res) {
  const { address } = req.body;
  // using Geocoding API to convert address to lat long
  const response = await fetch(`https://maps.googleapis.com/maps/api/geocode/json?address=${address}&key=${GOOGLE_MAPS_API_KEY}`);
  const data = await response.json();
  const location = data.results?.[0]?.geometry?.location;
  res.status(200).json({
    success: true,
    data: {
      lat: location?.lat || 0,
      lng: location?.lng || 0,
    }
  });
}