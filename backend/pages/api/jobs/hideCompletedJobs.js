import { allowCORS } from "@/@common-utils/allowCORS";
import { putWP } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

const hideCompletedJobs = async (req, res) => {
  try {
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);
    if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });

    if (req.method === "PUT") {
      const { jobsId } = req.body;
      const jobIds = Array.isArray(jobsId) ? jobsId : [jobsId];
      const updatePromises = jobIds.map(id => 
        putWP(`/jobs/${id}`, {
          job_status: "Hidden",
        })
      );
      
      const results = await Promise.all(updatePromises);
      
      return res.json({
        success: true,
        data: results,
      });
    }
    return res.json({ success: false, code: 405, message: "Method not allowed" });
  } catch (error) {
    console.log('error', error);
    return res.json({ success: false, code: 500, message: "Internal server error", error: error.message });
  }
}

export default hideCompletedJobs;