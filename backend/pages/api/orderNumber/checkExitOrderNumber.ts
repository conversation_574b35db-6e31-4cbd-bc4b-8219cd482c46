import { allowCORS } from "@/@common-utils/allowCORS";
import { getWPFilterORM } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

const checkExitOrderNumber = async (req, res) => {
    await allowCORS(req, res);
    if (req.method !== "GET") {
        res.json({
            success: false,
            message: "Method not allowed",
        });
        return;
    }
    const user = await checkFirebaseAuth(req);
    if (!user) {
        res.json({
            success: false,
            code: 403,
            message: "You need to login first.",
        });
        return;
    }
    const { order_number } = req.query;
    console.log('order_number', order_number);
    
    if (!order_number) {
        res.json({
            success: false,
            message: "Order number is required.",
        });
        return;
    }
    const findUser = await getWPFilterORM("/app_user", { 
        order_number: {
            value: order_number,
            compare: '='  // Using exact match instead of <PERSON>I<PERSON>
        }
    }, "id");
    
    if (findUser.length > 0) {
        res.json({
            success: true,
            message: "Order number already exists.",
        });
        return;
    }

    res.json({
        success: false,
        message: "Order number is available.",
    });
};

export default checkExitOrderNumber;
