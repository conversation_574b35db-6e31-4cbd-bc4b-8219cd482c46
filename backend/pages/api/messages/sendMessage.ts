import { checkFirebaseAuth } from '@/@common-utils/checkFirebaseAuth';
import { postWP, putWP, getWPFilterORM } from '@/@common-utils/callWP';
import { now } from "@/@common-utils/formatTime";
import { sendNoti } from "@/@common-utils/sendNoti";

const Pusher = require("pusher");

const pusher = new Pusher({
  appId: "*******",
  key: "9258dcf2124012d284ac",
  secret: "9aa242cb3ad0e67dd93f",
  cluster: "eu",
  useTLS: true
});


export default async function sendMessage(req, res) {
  const user = await checkFirebaseAuth(req);
  
  if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });
  const { conversation_id, unique_email_id, message, receiver_email } = req.body;
  
  const newMessage = await postWP('/chat_message', {
    sender_email: user.email,
    message,
    title: `${unique_email_id} | ${now('DD/MM/YYYY HH:mm:ss')} | ` + (message.length < 100 ? message : message.substring(0, 100) + '...'),
    unique_email_id,
    conversation: conversation_id,
    status: 'publish',
  });

  // SEND MESSAGE TO PUSHER
  await pusher.trigger("conversation-"+conversation_id, "chat", {
    message: {
      sender_email: user.email,
      message,
      message_id: newMessage.id,
    }
  });

  const senderUser = await getWPFilterORM("/app_user", {
    email: user.email,
  });

  const senderName = senderUser[0]?.user_name || "Someone";

  sendNoti(
    receiver_email,
    {
      title: "New Message",
      text: `You have a new message from ${senderName}`,
      html: `<p>You have a new message from ${senderName}</p>`,
    },
    {
      mobile: true,
      mail: false,
    },
    "Chat",
    {
      conversationId: conversation_id,
    }
  );
  const userindex = unique_email_id.indexOf(user.email) === 0 ? 0 : 1;

  // SAVE LATEST MESSAGE TO CONVERSATION
  await putWP('/conversation/'+conversation_id, {
    last_message: message,
    last_send_message_user: user.email,
    read_status: 'unread',
    ['last_seen_user_' + (userindex + 1)]: now('DD/MM/YYYY HH:mm:ss'),
    ['read_status_user_' + (userindex === 1? 2:1)]: "unread", 
  });

  res.status(200).json({
    success: true,
    data: newMessage,
  });
}