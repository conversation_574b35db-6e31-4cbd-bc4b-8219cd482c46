import { getWPFilterORM, postWP } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

type TGetConversationDetailPayload = {
  id: number 
} | {
  members: Array<{
    email: string,
    name: string,
  }>
}

export default async function(req, res) {
  if (req.method !== 'POST') return res.json({ success: false, message: "Method not allowed." });
  const user = await checkFirebaseAuth(req);
  if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });
  const { id, members } = req.body;
  try {
    if (!!id) {
      const data = await getWPFilterORM('/conversation/'+id);
      return res.json({ success: true, data });
    }
    if (!members || members.length < 2) return res.json({ success: false, message: "Members must be at least 2." });
    const sortedMembers = members.map(v => ({ ...v, email: v.email.toLowerCase() })).sort((a, b) => a.email.localeCompare(b.email));
    
    const user_emails = sortedMembers.map(v => v.email);
    const user_names = sortedMembers.map(v => v.name);
    const unique_email_id = user_emails.join('_');
    
    const find = await getWPFilterORM('/conversation', {
      unique_email_id
    });
    if (find.length > 0) {
      return res.json({ success: true, data: find[0] });
    }
    // create new conversation
    const newConversation = await postWP('/conversation', {
      user_emails,
      unique_email_id,
      title: `${user_names.join(' and ')} (${user_emails.join(' - ')})`,
      last_message: '',
      last_seen_timestamp: [0, 0],
      chat_messages: [],
      status: 'publish',
    });
    res.json({ success: true, data: newConversation });
  } catch (error) {
    console.log('error GetConversationDetail', error);
  }
}