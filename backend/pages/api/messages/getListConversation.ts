import { getWPFilterORM } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";


export default async function(req, res) {
  const user = await checkFirebaseAuth(req);
  if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });
  const { email } = user;
  const data = await getWPFilterORM('/conversation', {
    user_emails: email
  });
  res.json({
    success: true,
    data
  });
}