import { deleteWP, getWPFilterORM } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

export default async function(req, res) {
  console.log("DELETE CONVERSATION API CALLED", req.method);
  if (req.method !== 'DELETE') return res.json({ success: false, message: "Method not allowed." });
  console.log("CHECK FIREBASE AUTH");
  const user = await checkFirebaseAuth(req);
  if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });
  console.log("USER AUTHENTICATED", user);
  const { id } = req.body;
  if (!id) return res.json({ success: false, message: "Conversation ID is required." });
  
  try {
    const conversation = await getWPFilterORM('/conversation/'+id);
    
    if (!conversation) {
      return res.json({ success: false, message: "Conversation not found." });
    }
    
    const userEmails = conversation.user_emails || [];
    if (!userEmails.includes(user.email)) {
      return res.json({ success: false, code: 403, message: "You are not authorized to delete this conversation." });
    }
    
    const result = await deleteWP('/conversation/'+id);
    
    return res.json({
      success: true,
      message: "Conversation deleted successfully",
      data: result
    });
    
  } catch (error) {
    console.log('error DeleteConversation', error);
    return res.json({ 
      success: false, 
      message: "Failed to delete conversation", 
      error: error.message 
    });
  }
} 