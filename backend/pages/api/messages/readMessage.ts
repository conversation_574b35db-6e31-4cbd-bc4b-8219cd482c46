import { checkFirebaseAuth } from '@/@common-utils/checkFirebaseAuth';
import { getWP, putWP } from '@/@common-utils/callWP';

export default async function readMessage(req, res) {
  try {
    const user = await checkFirebaseAuth(req);
    
    if (!user) {
      return res.json({ success: false, code: 403, message: "You need to login first." });
    }

    const { conversation_id } = req.body;
    
    if (!conversation_id) {
      return res.json({ success: false, message: "Conversation ID is required." });
    }
    
    const conversation = await getWP('/conversation/'+conversation_id);
    
    if (!conversation) {
      return res.json({ success: false, code: 404, message: "Conversation not found." });
    }

    // Check if user is part of the conversation
    if (!conversation.user_emails?.includes(user.email)) {
      return res.json({ success: false, code: 403, message: "You are not authorized to access this conversation." });
    }

    const userIndex = conversation.unique_email_id.indexOf(user.email) === 0 ? 0 : 1;
    const otherUserIndex = userIndex === 0 ? 1 : 0;

    // Only update the read status for the current user
    const updateData = {
      [`read_status_user_${userIndex + 1}`]: 'read',
    };

    // If both users have read, update the general read_status
    if (
      (userIndex === 0 && conversation.read_status_user_2 === 'read') ||
      (userIndex === 1 && conversation.read_status_user_1 === 'read')
    ) {
      updateData.read_status = 'read';
    }

    await putWP('/conversation/'+conversation_id, updateData);
    console.log('updateData', updateData);
    res.status(200).json({
      success: true,
      data: "Message marked as read.",
    });
  } catch (error) {
    console.error('Error in readMessage:', error);
    res.status(500).json({
      success: false,
      message: "Failed to mark message as read.",
      error: error.message
    });
  }
}