import { allowCORS } from "@/@common-utils/allowCORS";
import { postWP, getWP, getWPFilterORM } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

const jobsApplicationAPI = async (req, res) => {
  try {
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);
    if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });

    if (req.method === "GET") {
      const filters = {
        ...req.query,
        job_status: {
          value: 'Hidden',
          compare: '!=',
        }
      };
      
      const jobs = await getWPFilterORM(`/job_application`, filters);
      if (!jobs) {
        return res.json({
          success: false,
          message: "Failed to get job.",
        });
      }
      return res.json({
        success: true,
        data: jobs,
      });
    }
  } catch (error) {
    console.log('error', error);
  }
};

export default jobsApplicationAPI;
