import { allowCORS } from "@/@common-utils/allowCORS";
import { postWP, getWP, getWPFilterORM } from "@/@common-utils/callWP";
import { sendNoti } from "@/@common-utils/sendNoti";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

export const jobApplicationAPI = async (req, res) => {
  try {
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);
    console.log('user', user);
    if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });

    if (req.method === "POST") {
      const { tradesperson_email, tradesperson, job, job_status, business } = req.body;
      if (tradesperson_email !== user.email) {
        res.json({
          success: false,
          message: "You are not allowed to do this action",
        });
        return;
      }
      const jobDetail = await getWP(`/jobs/${job}`);
      if (!jobDetail) {
        res.json({
          success: false,
          message: "Failed to get jobDetail.",
        });
        return;
      }
      if (jobDetail?.job_status[0] !== "Open") {
        res.json({
          success: false,
          message: "This job is not open.",
        });
        return;
      }

      const businessData = await getWP(`/app_user/${business[0]}?_fields=id,email`)

      const jobApplication = await postWP("/job_application",{
        tradesperson_email,
        title: tradesperson_email,
        tradesperson,
        job,
        job_status,
        status: "publish",
        business,
        business_email: businessData.email,
      });
  
      if (!jobApplication) {
        res.json({
          success: false,
          message: "Failed to create jobApplication.",
        });
        return;
      }
      sendNoti(
        jobApplication?.business[0]?.email,
        {
          title: "New application",
          text: `New application by ${jobApplication?.tradesperson[0].user_name}`,
          html: `<p>A application by ${jobApplication?.tradesperson[0].user_name}</p>`,
        },
        {
          mobile: true,
          mail: false,
        },
        "BusinessApplicationsOfAJob",
        {
          jobId: job,
          isNoti: true,
        }
      );
      res.json({
        success: true,
        data: jobApplication,
      });
    }
    if (req.method === "GET") {
      const jobApplication = await getWP(`/job_application`);
      if (!jobApplication) {
        res.json({
          success: false,
          message: "Failed to get jobApplication.",
        });
        return;
      }
      res.json({
        success: true,
        data: jobApplication,
      });
    }
  } catch (error) {
    console.log('error', error);
  }
};

export default jobApplicationAPI;
