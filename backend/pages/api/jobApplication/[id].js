import { allowCORS } from "@/@common-utils/allowCORS";
import { postWP, putWP, getWP, deleteWP } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

export default async function jobApplicationById(req, res) {
  try {
    await allowCORS(req, res);
    // const {id} = req.query;
    // const user = await checkFirebaseAuth(req);
    // if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });

    // if (req.method === "GET") {
    //   const jobApplication = await getWP(`/job_application/${id}`);
    //   if (!jobApplication) {
    //     res.json({
    //       success: false,
    //       message: "Failed to create jobApplication.",
    //     });
    //     return;
    //   }
    //   res.json({
    //     success: true,
    //     data: jobApplication,
    //   });
    // }
    // if (req.method === "PUT") {
    //   const { tradesperson_email, tradesperson, job, job_status} = req.body;

    //   const jobApplication = await putWP(`/job_application/${id}`,{
    //     ...(tradesperson_email ? { tradesperson_email } : {}),
    //     ...(tradesperson ? { tradesperson } : {}),
    //     ...(job ? { job } : {}),
    //     ...(job_status ? { job_status } : {}),
    //   });
    //   if (!jobApplication) {
    //     res.json({
    //       success: false,
    //       message: "Failed to update jobApplication.",
    //     });
    //     return;
    //   }
    //   res.json({
    //     success: true,
    //     data: jobApplication,
    //   });
    // }
    // if (req.method === "DELETE") {
    //   const jobApplication = await deleteWP(`/job_application/${id}`);
    //   if (!jobApplication) {
    //     res.json({
    //       success: false,
    //       message: "Failed to delete jobApplication.",
    //     });
    //     return;
    //   }
    //   res.json({
    //     success: true,
    //     message: 'jobApplication id: ' + id + ' deleted.',
    //   });
    // }
  } catch (error) {
    console.log('error', error);
  }
};
