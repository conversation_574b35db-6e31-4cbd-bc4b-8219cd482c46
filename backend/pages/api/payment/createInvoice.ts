import { allowCORS } from "@/@common-utils/allowCORS";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { getWPFilterORM, putWP, uploadWP } from "@/@common-utils/callWP";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";
import { sendSMTPEmail } from "@/@common-utils/sendNoti";

const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);

async function saveAndUploadInvoice(pdfLink: string, fileName: string) {
  // pdfLink is a link to the PDF file
  const pdfResponse = await fetch(pdfLink);
  const pdfBuffer = await pdfResponse.arrayBuffer();

  // Save PDF buffer to temporary file
  const tempFilePath = path.join(os.tmpdir(), fileName);
  fs.writeFileSync(tempFilePath, Buffer.from(pdfBuffer));

  // Upload to WordPress
  const uploadResult = await uploadWP({
    name: fileName,
    filePath: tempFilePath,
    contentType: "application/pdf",
    size: pdfLink.length,
  });
  // Clean up temp file
  fs.unlinkSync(tempFilePath);

  return uploadResult.source_url || uploadResult.guid?.rendered;
}

const createInvoice = async (req, res) => {
  try {
    await allowCORS(req, res);
    if (req.method !== "POST") {
      return res.json({ success: false, message: "Method not allowed" });
    }

    const user = await checkFirebaseAuth(req);
    if (!user) {
      return res.json({ success: false, message: "Authentication required" });
    }
    const fullUser = await getWPFilterORM(
      `/app_user`,
      {
        email: user.email,
      },
      "id,invoices,business_stripe_id"
    );

    if (!fullUser[0]) {
      return res.json({ success: false, message: "User not found" });
    }

    const {
      amount,
      currency,
      order_number,
      authorization_name,
      invoice_email_address,
      signature_base64,
    } = req.body;
    // Convert base64 signature to image URL (you'll need to host this image somewhere)
    // let signatureUrl = '';
    // if (signature_base64) {
    //   const signatureBuffer = Buffer.from(signature_base64.split(',')[1], 'base64');
    //   const signatureFileName = `signature_${order_number}_${Date.now()}.png`;
      
    //   const tempSignaturePath = path.join(os.tmpdir(), signatureFileName);
    //   fs.writeFileSync(tempSignaturePath, signatureBuffer);

    //   const signatureUpload = await uploadWP({
    //     name: signatureFileName,
    //     filePath: tempSignaturePath,
    //     contentType: 'image/png',
    //     size: signatureBuffer.length,
    //   });

    //   fs.unlinkSync(tempSignaturePath);
      
    //   signatureUrl = signatureUpload.source_url || signatureUpload.guid?.rendered;
    // }

    // Create new Stripe customer if business_stripe_id doesn't exist
    let businessStripeId = fullUser[0].business_stripe_id;
    if (!businessStripeId) {
      const newCustomer = await stripe.customers.create({
        email: invoice_email_address,
        metadata: {
          wp_user_id: fullUser[0].id
        }
      });
      businessStripeId = newCustomer.id;
      
      // Update user with new Stripe ID
      await putWP(`/app_user/${fullUser[0].id}`, {
        business_stripe_id: businessStripeId
      });
    } else {
      // Existing code to update customer email
      await stripe.customers.update(businessStripeId, {
        email: invoice_email_address
      });
    }

    // Create invoice for customer to pay
    const invoice = await stripe.invoices.create({
      customer: businessStripeId,
      collection_method: 'send_invoice',
      days_until_due: 30,
      description: `Order Number: ${order_number}`,
      footer: `Authorized by: ${authorization_name}`,
      custom_fields: [
        {
          name: 'Order Number',
          value: order_number
        },
        {
          name: 'Authorized Name',
          value: authorization_name
        }
      ]
    });

    // Add invoice item
    await stripe.invoiceItems.create({
      customer: businessStripeId,
      invoice: invoice.id,
      amount: amount,
      description: "Credit Purchase",
    });

    // Finalize and send the invoice
    await stripe.invoices.finalizeInvoice(invoice.id);
    await stripe.invoices.sendInvoice(invoice.id);

    // Get invoice PDF for records
    const invoicePdf = await stripe.invoices.retrieve(invoice.id);
    const pdfLink = invoicePdf.invoice_pdf;
    
    // Upload to WordPress
    const fileName = `invoice_${order_number}_${Date.now()}.pdf`;
    const invoiceUrl = await saveAndUploadInvoice(pdfLink, fileName);
    
    // Update user's invoices array
    let invoices = fullUser[0].invoices ? fullUser[0].invoices : [];
    invoices.push(invoiceUrl);
    await putWP(`/app_user/${fullUser[0].id}`, {
      invoices,
    });

    // Send custom email with invoice
    await sendSMTPEmail({
      from: "",
      to: invoice_email_address,
      subject: `Invoice for Order #${order_number}`,
      html: `
        <p>Your invoice for Order #${order_number} has been created.</p>
        <p>You can download the invoice from the following link: ${invoiceUrl}</p>
        <p>To pay this invoice, please visit: ${invoicePdf.hosted_invoice_url}</p>
      `,
    });

    res.json({ 
      success: true, 
      invoiceUrl,
      hostedInvoiceUrl: invoicePdf.hosted_invoice_url
    });
  } catch (error) {
    console.error("Error creating invoice:", error);
    res.json({ success: false, message: error.message });
  }
};

export default createInvoice;
