import { allowCORS } from "@/@common-utils/allowCORS";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { getWPFilterORM, putWP } from "@/@common-utils/callWP";

const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);

const stripeCreateDeposit = async (req, res) => {
  try {
    await allowCORS(req, res);
    if (req.method !== "POST")
      return res.json({
        success: false,
        code: 405,
        message: "Method not allowed.",
      });
    const user = await checkFirebaseAuth(req);
    if (!user)
      return res.json({
        success: false,
        code: 403,
        message: "You need to login first.",
      });

    const { amount, currency } = req.body;
    const fullUser = await getWPFilterORM(
      `/app_user`,
      {
        email: user.email,
      },
      "id,business_stripe_id,user_type,user_name,email"
    );
    if (fullUser.user_type === "Reseller") {
      res.json({
        success: false,
        code: 400,
        message: "Resellers are not allowed to deposit funds.",
      });
    }

    if (!fullUser[0]?.business_stripe_id) {
      const userName = fullUser[0]?.user_name;
      const customer = await stripe.customers.create({
        name: userName,
        email: fullUser[0]?.email,
      });
      fullUser[0].business_stripe_id = customer.id;
      const updatedUser = await putWP(`/app_user/${fullUser[0]?.id}`, {
        business_stripe_id: customer.id,
      });
      if (updatedUser.code) {
        res.json({
          success: false,
          code: 500,
          message: "Failed to update user.",
        });
        return;
      }
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount),
        currency: currency?.toLowerCase() || "gbp",
        payment_method_types: ["card"],
        customer: fullUser[0]?.business_stripe_id,
        setup_future_usage: "on_session",
        metadata: {
          credits: 0,
        },
      });
      res.json({
        success: true,
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
      });
      return;
    }

    // fallback to first time payment
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount),
      currency: currency?.toLowerCase() || "gbp",
      payment_method_types: ["card"],
      customer: fullUser[0]?.business_stripe_id,
      setup_future_usage: "on_session",
    });
    res.json({
      success: true,
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
    });
    return;
  } catch (error) {
    console.log("error", error);
  }
};

export default stripeCreateDeposit;
