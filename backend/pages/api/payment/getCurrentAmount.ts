import { allowCORS } from "@/@common-utils/allowCORS";
import { getWPFilterORM } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { getCreditBalance, getCreditHistory } from "@/@common-utils/creditService";

const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);

const getCurrentAmount = async (req, res) => {
  try {
    await allowCORS(req, res);
    if (req.method !== "GET")
      return res.json({
        success: false,
        code: 405,
        message: "Method not allowed.",
      });
    const user = await checkFirebaseAuth(req);
    if (!user)
      return res.json({
        success: false,
        code: 403,
        message: "You need to login first.",
      });
    const fullUser = await getWPFilterORM(
      `/app_user`,
      {
        email: user.email,
      },
      "id,business_stripe_id"
    );

    if (!fullUser[0]?.business_stripe_id) {
      return res.json({
        success: true,
        data: {
          metadata: {
            credits: 0,
          },
          history: []
        },
      });
    }

    // Get balance and recent transaction history
    const [balance, history] = await Promise.all([
      getCreditBalance(fullUser[0].id),
      getCreditHistory(fullUser[0].id, 5)
    ]);

    res.json({
      success: true,
      data: {
        metadata: {
          credits: balance,
        },
        balance: balance,
        history: history
      },
    });
  } catch (error) {
    console.error("Error getting current amount:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};

export default getCurrentAmount;
