import { allowCORS } from "@/@common-utils/allowCORS";
import { getWPFilterORM } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);

const getCurrentAmount = async (req, res) => {
  try {
    await allowCORS(req, res);
    if (req.method !== "GET")
      return res.json({
        success: false,
        code: 405,
        message: "Method not allowed.",
      });
    const user = await checkFirebaseAuth(req);
    if (!user)
      return res.json({
        success: false,
        code: 403,
        message: "You need to login first.",
      });
    const fullUser = await getWPFilterORM(
      `/app_user`,
      {
        email: user.email,
      },
      "business_stripe_id"
    );

    if (!fullUser[0]?.business_stripe_id) {
      return res.json({
        success: true,
        data: {
          metadata: {
            credits: 0,
          },
        },
      });
    }

    const balance = await stripe.customers.retrieve(fullUser[0]?.business_stripe_id, {
      expand: ["cash_balance"],
    });
    res.json({
      success: true,
      data: balance,
    });
  } catch (error) {
    console.error("Error getting current amount:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};

export default getCurrentAmount;
