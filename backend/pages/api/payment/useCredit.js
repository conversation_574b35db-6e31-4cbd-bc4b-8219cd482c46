import { allowCORS } from "@/@common-utils/allowCORS";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { addCreditTransaction, getCreditBalance } from "@/@common-utils/creditService";
import { getWPFilterORM } from "@/@common-utils/callWP";

const useCredit = async (req, res) => {
  try {
    await allowCORS(req, res);
    if (req.method !== "POST") {
      return res.status(405).json({ success: false, message: "Method not allowed" });
    }
    
    const user = await checkFirebaseAuth(req);
    if (!user) {
      return res.status(403).json({ success: false, message: "Unauthorized" });
    }
    
    const { amount = 1, description, referenceId } = req.body;
    
    if (!description) {
      return res.status(400).json({ success: false, message: "Description is required" });
    }
    
    // Get user info
    const fullUser = await getWPFilterORM('/app_user', { email: user.email }, 'id,user_type');
    if (!fullUser[0]) {
      return res.status(404).json({ success: false, message: "User not found" });
    }
    
    if (fullUser[0]?.user_type === "Reseller") {
      return res.status(403).json({ success: false, message: "Resellers cannot use credits" });
    }
    
    // Check if user has enough credits
    const currentBalance = await getCreditBalance(fullUser[0].id);
    if (currentBalance < amount) {
      return res.status(400).json({ 
        success: false, 
        message: `Insufficient credits. Current balance: ${currentBalance}, Required: ${amount}` 
      });
    }
    
    // Use credits (negative amount)
    const result = await addCreditTransaction(
      fullUser[0].id,
      -amount,
      'usage',
      description,
      referenceId,
      'system'
    );
    
    res.json({ 
      success: true, 
      data: {
        creditsUsed: amount,
        newBalance: result.newBalance,
        transaction: result.transaction
      }
    });
  } catch (error) {
    console.error('Use credit error:', error);
    res.status(500).json({ success: false, message: error.message });
  }
};

export default useCredit;
