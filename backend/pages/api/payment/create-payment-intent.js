import { allowCORS } from "@/@common-utils/allowCORS";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { getWPFilterORM, putWP } from "@/@common-utils/callWP";

const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);

const paymentAPI = async (req, res) => {
  try {
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);

    if (!user) {
      return res
        .status(403)
        .json({
          success: false,
          code: 403,
          message: "You need to login first.",
        });
    }

    if (req.method === "POST") {
      const { amount, currency } = req.body;

      const fullUser = await getWPFilterORM(
        `/app_user`,
        {
          email: user.email,
        },
        "id,business_stripe_id,user_type,user_name,email"
      );
      if (fullUser.user_type === "Reseller") {
        res.json({
          success: false,
          code: 400,
          message: "Resellers are not allowed to deposit funds.",
        });
      }

      if (!fullUser[0]?.business_stripe_id) {
        const userName = fullUser[0]?.user_name;
        const customer = await stripe.customers.create({
          name: userName,
          email: fullUser[0]?.email,
        });
        fullUser[0].business_stripe_id = customer.id;
        const updatedUser = await putWP(`/app_user/${fullUser[0]?.id}`, {
          business_stripe_id: customer.id,
        });
        if (updatedUser.code) {
          res.json({
            success: false,
            code: 500,
            message: "Failed to update user.",
          });
          return;
        }
        const paymentIntent = await stripe.paymentIntents.create({
          amount: Math.round(amount),
          currency: currency?.toLowerCase() || "gbp",
          payment_method_types: ["card"],
          customer: fullUser[0]?.business_stripe_id,
          setup_future_usage: "on_session",
          metadata: {
            credits: 0,
          },
        });
        res.json({
          success: true,
          clientSecret: paymentIntent.client_secret,
          paymentIntentId: paymentIntent.id,
        });
        return;
      }

      // fallback to first time payment
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount),
        currency: currency?.toLowerCase() || "gbp",
        payment_method_types: ["card"],
        customer: fullUser[0]?.business_stripe_id,
        setup_future_usage: "on_session",
      });
      res.json({
        success: true,
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
      });
      return;
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  } catch (error) {
    console.error("Payment error:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};

export default paymentAPI;
