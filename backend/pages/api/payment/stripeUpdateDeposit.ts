import { allowCORS } from "@/@common-utils/allowCORS";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { getWPFilterORM, putWP } from "@/@common-utils/callWP";
import { addCreditTransaction } from "@/@common-utils/creditService";

const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);

const stripeUpdateDeposit = async (req, res) => {
  try {
    if (req.method !== "POST")
      return res.json({
        success: false,
        code: 405,
        message: "Method not allowed.",
      });
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);
    if (!user)
      return res.json({
        success: false,
        code: 403,
        message: "You need to login first.",
      });

    const { new_credits } = req.body;
    const fullUser = await getWPFilterORM(
      `/app_user`,
      {
        email: user.email,
      },
      "id,user_type,business_stripe_id"
    );

    if (fullUser[0]?.user_type === "Reseller") {
      return res.json({
        success: false,
        code: 400,
        message: "Resellers are not allowed to deposit funds.",
      });
    }

    // Get current credits from Stripe
    const customer = await stripe.customers.retrieve(fullUser[0]?.business_stripe_id);
    const currentCredits = parseInt(customer.metadata.credits || 0);
    const creditsPurchased = new_credits - currentCredits;

    // Update Stripe metadata
    await stripe.customers.update(fullUser[0]?.business_stripe_id, {
      metadata: { credits: new_credits.toString() }
    });

    // Create transaction record if credits were purchased
    if (creditsPurchased > 0) {
      await addCreditTransaction(
        fullUser[0].id,
        creditsPurchased,
        'purchase',
        `Purchased ${creditsPurchased} credits via Stripe`,
        null,
        'system'
      );
    }

    console.log("customer updated with credits:", new_credits);
    res.json({
      success: true,
      data: { credits: new_credits },
    });
    return;
  } catch (error) {
    console.log("error", error);
    res.status(500).json({
      success: false,
      message: error.message,
    });
  }
};

export default stripeUpdateDeposit;
