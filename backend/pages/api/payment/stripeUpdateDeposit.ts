import { allowCORS } from "@/@common-utils/allowCORS";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { getWPFilterORM, putWP } from "@/@common-utils/callWP";

const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);

const stripeUpdateDeposit = async (req, res) => {
  try {
    if (req.method !== "POST")
      return res.json({
        success: false,
        code: 405,
        message: "Method not allowed.",
      });
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);
    if (!user)
      return res.json({
        success: false,
        code: 403,
        message: "You need to login first.",
      });

    const { new_credits } = req.body;
    const fullUser = await getWPFilterORM(
      `/app_user`,
      {
        email: user.email,
      },
      "user_type,business_stripe_id"
    );

    if (fullUser.user_type === "Reseller") {
      res.json({
        success: false,
        code: 400,
        message: "Resellers are not allowed to deposit funds.",
      });
    }

    const customer = await stripe.customers.update(
      fullUser[0]?.business_stripe_id,
      {
        metadata: {
          credits: new_credits,
        },
      }
    );
    console.log("customer", customer);
    res.json({
      success: true,
      data: customer,
    });
    return;
  } catch (error) {
    console.log("error", error);
    res.status(500).json({
      success: false,
      message: error.message,
    });
  }
};

export default stripeUpdateDeposit;
