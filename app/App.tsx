/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import React, { useEffect } from 'react';
import { Layout } from './Layout';
import { FormProvider, useForm } from 'react-hook-form';
import { AuthProvider } from './context/AuthContext';
import { OneSignal, LogLevel } from 'react-native-onesignal';
import { NavigationContainer } from '@react-navigation/native';
import { handleNotification } from './src/utils/handleNotification';
import { StripeProvider } from '@stripe/stripe-react-native';
import { navigationRef } from './src/utils/navigationRef';

export default function App() {
  const methods = useForm();
  function navigate(name, params) {
    if (navigationRef.isReady()) {
      navigationRef.navigate(name, params);
    }
  }

  useEffect(() => {
    OneSignal.Debug.setLogLevel(LogLevel.Verbose);
    // Initialize OneSignal
    OneSignal.initialize('************************************');

    // Request notification permissions
    OneSignal.Notifications.requestPermission(true);
    const notificationOpenedHandler = (openedEvent) => {
      console.log('OneSignal: notification opened:', JSON.stringify(openedEvent));
      handleNotification(openedEvent, navigate);
    };

    OneSignal.Notifications.addEventListener('click', notificationOpenedHandler);
  }, []);

  return (
    <FormProvider {...methods}>
      <AuthProvider>
        <StripeProvider
          publishableKey={'pk_live_51PCLAgP3GNjLk0k03o24UplplHPsddYV8JgKNY5GYUPQQfKITvx4ZC8LJwiWXgfSRX24I9aWM4Yt2n3xc8pthbff00OiVfRh24'}
        >
          <NavigationContainer ref={navigationRef}>
            <Layout />
          </NavigationContainer>
        </StripeProvider>
      </AuthProvider>
    </FormProvider>
  );
}
