PODS:
  - boost (1.76.0)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.72.17)
  - FBReactNativeSpec (0.72.17):
    - RCT-<PERSON>olly (= 2021.07.22.00)
    - RCTRequired (= 0.72.17)
    - RCTTypeSafety (= 0.72.17)
    - React-Core (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - Firebase/Auth (10.21.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 10.21.0)
  - Firebase/CoreOnly (10.21.0):
    - FirebaseCore (= 10.21.0)
  - FirebaseAppCheckInterop (10.29.0)
  - FirebaseAuth (10.21.0):
    - FirebaseAppCheckInterop (~> 10.17)
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
    - RecaptchaInterop (~> 100.0)
  - FirebaseCore (10.21.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - fmt (6.2.1)
  - glog (0.3.5)
  - Google-Maps-iOS-Utils (4.2.2):
    - Google-Maps-iOS-Utils/Clustering (= 4.2.2)
    - Google-Maps-iOS-Utils/Geometry (= 4.2.2)
    - Google-Maps-iOS-Utils/GeometryUtils (= 4.2.2)
    - Google-Maps-iOS-Utils/Heatmap (= 4.2.2)
    - Google-Maps-iOS-Utils/QuadTree (= 4.2.2)
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/Clustering (4.2.2):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/Geometry (4.2.2):
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/GeometryUtils (4.2.2):
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/Heatmap (4.2.2):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/QuadTree (4.2.2):
    - GoogleMaps (~> 7.3)
  - GoogleMaps (7.4.0):
    - GoogleMaps/Maps (= 7.4.0)
  - GoogleMaps/Base (7.4.0)
  - GoogleMaps/Maps (7.4.0):
    - GoogleMaps/Base
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMSessionFetcher/Core (3.5.0)
  - hermes-engine (0.72.17):
    - hermes-engine/Pre-built (= 0.72.17)
  - hermes-engine/Pre-built (0.72.17)
  - libevent (2.1.12)
  - NWWebSocket (0.5.4)
  - OneSignalXCFramework (5.2.9):
    - OneSignalXCFramework/OneSignalComplete (= 5.2.9)
  - OneSignalXCFramework/OneSignal (5.2.9):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalLiveActivities
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalComplete (5.2.9):
    - OneSignalXCFramework/OneSignal
    - OneSignalXCFramework/OneSignalInAppMessages
    - OneSignalXCFramework/OneSignalLocation
  - OneSignalXCFramework/OneSignalCore (5.2.9)
  - OneSignalXCFramework/OneSignalExtension (5.2.9):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalInAppMessages (5.2.9):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalLiveActivities (5.2.9):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalLocation (5.2.9):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalNotifications (5.2.9):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalOSCore (5.2.9):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalOutcomes (5.2.9):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalUser (5.2.9):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
  - PromisesObjC (2.4.0)
  - pusher-websocket-react-native (1.3.1):
    - PusherSwift (~> 10.1.5)
    - React
  - PusherSwift (10.1.5):
    - NWWebSocket (~> 0.5.4)
    - TweetNacl (~> 1.0.0)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.72.17)
  - RCTTypeSafety (0.72.17):
    - FBLazyVector (= 0.72.17)
    - RCTRequired (= 0.72.17)
    - React-Core (= 0.72.17)
  - React (0.72.17):
    - React-Core (= 0.72.17)
    - React-Core/DevSupport (= 0.72.17)
    - React-Core/RCTWebSocket (= 0.72.17)
    - React-RCTActionSheet (= 0.72.17)
    - React-RCTAnimation (= 0.72.17)
    - React-RCTBlob (= 0.72.17)
    - React-RCTImage (= 0.72.17)
    - React-RCTLinking (= 0.72.17)
    - React-RCTNetwork (= 0.72.17)
    - React-RCTSettings (= 0.72.17)
    - React-RCTText (= 0.72.17)
    - React-RCTVibration (= 0.72.17)
  - React-callinvoker (0.72.17)
  - React-Codegen (0.72.17):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.17)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.17)
    - React-Core/RCTWebSocket (= 0.72.17)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.72.17)
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.17)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.17)
    - React-Codegen (= 0.72.17)
    - React-Core/CoreModulesHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-RCTBlob
    - React-RCTImage (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.72.17):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.17)
    - React-debug (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-jsinspector (= 0.72.17)
    - React-logger (= 0.72.17)
    - React-perflogger (= 0.72.17)
    - React-runtimeexecutor (= 0.72.17)
  - React-debug (0.72.17)
  - React-hermes (0.72.17):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - RCT-Folly/Futures (= 2021.07.22.00)
    - React-cxxreact (= 0.72.17)
    - React-jsi
    - React-jsiexecutor (= 0.72.17)
    - React-jsinspector (= 0.72.17)
    - React-perflogger (= 0.72.17)
  - React-jsi (0.72.17):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.72.17):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-perflogger (= 0.72.17)
  - React-jsinspector (0.72.17)
  - React-logger (0.72.17):
    - glog
  - react-native-add-calendar-event (5.0.0):
    - React-Core
  - react-native-document-picker (8.2.1):
    - React-Core
  - react-native-geolocation-service (5.3.1):
    - React
  - react-native-google-maps (1.11.3):
    - Google-Maps-iOS-Utils (= 4.2.2)
    - GoogleMaps (= 7.4.0)
    - React-Core
  - react-native-image-picker (7.1.2):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - react-native-maps (1.11.3):
    - React-Core
  - react-native-onesignal (5.2.8):
    - OneSignalXCFramework (= 5.2.9)
    - React (< 1.0.0, >= 0.13.0)
  - react-native-safe-area-context (4.7.1):
    - React-Core
  - react-native-webview (13.8.6):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - React-NativeModulesApple (0.72.17):
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.72.17)
  - React-RCTActionSheet (0.72.17):
    - React-Core/RCTActionSheetHeaders (= 0.72.17)
  - React-RCTAnimation (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.17)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTAnimationHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTAppDelegate (0.72.17):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-NativeModulesApple
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.72.17):
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTBlobHeaders (= 0.72.17)
    - React-Core/RCTWebSocket (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-RCTNetwork (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTImage (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.17)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTImageHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-RCTNetwork (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTLinking (0.72.17):
    - React-Codegen (= 0.72.17)
    - React-Core/RCTLinkingHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTNetwork (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.17)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTNetworkHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTSettings (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.17)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTSettingsHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTText (0.72.17):
    - React-Core/RCTTextHeaders (= 0.72.17)
  - React-RCTVibration (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTVibrationHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-rncore (0.72.17)
  - React-runtimeexecutor (0.72.17):
    - React-jsi (= 0.72.17)
  - React-runtimescheduler (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-runtimeexecutor
  - React-utils (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-debug
  - ReactCommon/turbomodule/bridging (0.72.17):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.17)
    - React-cxxreact (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-logger (= 0.72.17)
    - React-perflogger (= 0.72.17)
  - ReactCommon/turbomodule/core (0.72.17):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.17)
    - React-cxxreact (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-logger (= 0.72.17)
    - React-perflogger (= 0.72.17)
  - RecaptchaInterop (100.0.0)
  - RNCAsyncStorage (1.19.1):
    - React-Core
  - RNDateTimePicker (7.4.1):
    - React-Core
  - RNFBApp (19.0.0):
    - Firebase/CoreOnly (= 10.21.0)
    - React-Core
  - RNFBAuth (19.0.0):
    - Firebase/Auth (= 10.21.0)
    - React-Core
    - RNFBApp
  - RNGestureHandler (2.12.1):
    - React-Core
  - RNKeychain (10.0.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNPermissions (4.1.5):
    - React-Core
  - RNScreens (3.23.0):
    - React-Core
    - React-RCTImage
  - RNVectorIcons (10.0.3):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - SocketRocket (0.6.1)
  - Stripe (23.28.3):
    - StripeApplePay (= 23.28.3)
    - StripeCore (= 23.28.3)
    - StripePayments (= 23.28.3)
    - StripePaymentsUI (= 23.28.3)
    - StripeUICore (= 23.28.3)
  - stripe-react-native (0.38.6):
    - React-Core
    - Stripe (~> 23.28.0)
    - StripeApplePay (~> 23.28.0)
    - StripeFinancialConnections (~> 23.28.0)
    - StripePayments (~> 23.28.0)
    - StripePaymentSheet (~> 23.28.0)
    - StripePaymentsUI (~> 23.28.0)
  - StripeApplePay (23.28.3):
    - StripeCore (= 23.28.3)
  - StripeCore (23.28.3)
  - StripeFinancialConnections (23.28.3):
    - StripeCore (= 23.28.3)
    - StripeUICore (= 23.28.3)
  - StripePayments (23.28.3):
    - StripeCore (= 23.28.3)
    - StripePayments/Stripe3DS2 (= 23.28.3)
  - StripePayments/Stripe3DS2 (23.28.3):
    - StripeCore (= 23.28.3)
  - StripePaymentSheet (23.28.3):
    - StripeApplePay (= 23.28.3)
    - StripeCore (= 23.28.3)
    - StripePayments (= 23.28.3)
    - StripePaymentsUI (= 23.28.3)
  - StripePaymentsUI (23.28.3):
    - StripeCore (= 23.28.3)
    - StripePayments (= 23.28.3)
    - StripeUICore (= 23.28.3)
  - StripeUICore (23.28.3):
    - StripeCore (= 23.28.3)
  - TouchID (4.4.1):
    - React
  - TweetNacl (1.0.2)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - Google-Maps-iOS-Utils (= 4.2.2)
  - GoogleMaps (= 7.4.0)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - OneSignalXCFramework (= 5.2.9)
  - "pusher-websocket-react-native (from `../node_modules/@pusher/pusher-websocket-react-native`)"
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-add-calendar-event (from `../node_modules/react-native-add-calendar-event`)
  - react-native-document-picker (from `../node_modules/react-native-document-picker`)
  - react-native-geolocation-service (from `../node_modules/react-native-geolocation-service`)
  - react-native-google-maps (from `../node_modules/react-native-maps`)
  - react-native-image-picker (from `../node_modules/react-native-image-picker`)
  - react-native-maps (from `../node_modules/react-native-maps`)
  - react-native-onesignal (from `../node_modules/react-native-onesignal`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBAuth (from `../node_modules/@react-native-firebase/auth`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNKeychain (from `../node_modules/react-native-keychain`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - "stripe-react-native (from `../node_modules/@stripe/stripe-react-native`)"
  - TouchID (from `../node_modules/react-native-touch-id`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseCore
    - FirebaseCoreInternal
    - fmt
    - Google-Maps-iOS-Utils
    - GoogleMaps
    - GoogleUtilities
    - GTMSessionFetcher
    - libevent
    - NWWebSocket
    - OneSignalXCFramework
    - PromisesObjC
    - PusherSwift
    - RecaptchaInterop
    - SocketRocket
    - Stripe
    - StripeApplePay
    - StripeCore
    - StripeFinancialConnections
    - StripePayments
    - StripePaymentSheet
    - StripePaymentsUI
    - StripeUICore
    - TweetNacl

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
  pusher-websocket-react-native:
    :path: "../node_modules/@pusher/pusher-websocket-react-native"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-add-calendar-event:
    :path: "../node_modules/react-native-add-calendar-event"
  react-native-document-picker:
    :path: "../node_modules/react-native-document-picker"
  react-native-geolocation-service:
    :path: "../node_modules/react-native-geolocation-service"
  react-native-google-maps:
    :path: "../node_modules/react-native-maps"
  react-native-image-picker:
    :path: "../node_modules/react-native-image-picker"
  react-native-maps:
    :path: "../node_modules/react-native-maps"
  react-native-onesignal:
    :path: "../node_modules/react-native-onesignal"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBAuth:
    :path: "../node_modules/@react-native-firebase/auth"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNKeychain:
    :path: "../node_modules/react-native-keychain"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  stripe-react-native:
    :path: "../node_modules/@stripe/stripe-react-native"
  TouchID:
    :path: "../node_modules/react-native-touch-id"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 7dcd2de282d72e344012f7d6564d024930a6a440
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBLazyVector: 66398fc2381d8fa1eee4c0f80d931587a7b927e8
  FBReactNativeSpec: 0f8cecf999d709dba7626bbf565b1b5f8f46a5c1
  Firebase: 4453b799f72f625384dc23f412d3be92b0e3b2a0
  FirebaseAppCheckInterop: 6a1757cfd4067d8e00fccd14fcc1b8fd78cfac07
  FirebaseAuth: 42f07198c4ed6b55643a147ae682d32200f3ff6d
  FirebaseCore: 74f647ad9739ea75112ce6c3b3b91f5488ce1122
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  Google-Maps-iOS-Utils: f77eab4c4326d7e6a277f8e23a0232402731913a
  GoogleMaps: 032f676450ba0779bd8ce16840690915f84e57ac
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  hermes-engine: 982096772bd947125ee3b4f72ace6cb9a33f1d02
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  NWWebSocket: 040d22f23438cc09aaeabf537beff67699c3c76d
  OneSignalXCFramework: f5b2a3c4f130e4c910ead7bb25bed7455e976fbf
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  pusher-websocket-react-native: e40c49a1e4ec96d4157375aebcf44943f0f8f62f
  PusherSwift: cad631bad86cfff4b8458dce1310a7774e469b1f
  RCT-Folly: 424b8c9a7a0b9ab2886ffe9c3b041ef628fd4fb1
  RCTRequired: 01c639ec840ee03928b2d65f5cd5297d737b3834
  RCTTypeSafety: 9623592521a1576363baf3d6ab8d164cfe9062bf
  React: 3c0beeda318c3c515a6bb2c1f197b55bd731aa43
  React-callinvoker: 0cd6ff2cdd80255c82cd4628fc925df1e7133a1a
  React-Codegen: 4b9fd690baa0098aa9759fdccc1467409171216e
  React-Core: 8946edeb9272e1a5a52559c5793e6ea0e9e77fec
  React-CoreModules: f37b2cb94073a720511fe54f821762fb1015f30c
  React-cxxreact: dec3959d439708cb7dd73b46a11ed64c3eea79da
  React-debug: fc03bbe567421af7f7837bdff93ad9d5b7c0ab9d
  React-hermes: f3b6b278c4ff7e6664a86b2bf964a4dc4ae72d34
  React-jsi: 6ec4bd4cd929ae9d468b4984b0ae2c657aeeb2da
  React-jsiexecutor: 8dc585381e476c3ff2e9468f444c90c4d1d5b874
  React-jsinspector: 853b8631b908636bb09ef77cb217376c38a0c8ff
  React-logger: 9ca44bb5703bf2355f3c2d2e5e67bfe98ca2dc34
  react-native-add-calendar-event: b9802a8be8ad4beb0dcb3efb28969c077558fb29
  react-native-document-picker: 69ca2094d8780cfc1e7e613894d15290fdc54bba
  react-native-geolocation-service: 608e1da71a1ac31b4de64d9ef2815f697978c55b
  react-native-google-maps: b80d3947411fcf462ec420c3e6122b7fa24beb9a
  react-native-image-picker: b561409791bdb2b97fe2c4bfdbc50981f8067a68
  react-native-maps: 79610ffcd0ec2ca8e0cefb48aa8ae7c62a75d7cc
  react-native-onesignal: 1795c532f8bd86c3307a032621c17d85998402d0
  react-native-safe-area-context: 9697629f7b2cda43cf52169bb7e0767d330648c2
  react-native-webview: e521df378b9d03a17ea66b6724bfc771fd788502
  React-NativeModulesApple: 9baff5b154326151add253f2c5bcb93d20c33742
  React-perflogger: 785b0063af5178298a61b54bb46aae9a19c7bbb5
  React-RCTActionSheet: 84f37b34bd77249263ace75471d6664393c29972
  React-RCTAnimation: cbd40a6bb62866d0f90b318a02ead456ec3ad523
  React-RCTAppDelegate: aed41a87c92f8c889f54b11c607837c8dafd7bf9
  React-RCTBlob: 389c7294855130b1cbb3efa3fdc7c0f8b7e33ca8
  React-RCTImage: b02b19272c05c01a1ab94a45c2ff11ede8726a8a
  React-RCTLinking: a64fcab6d9aa8c67e7771299dc6a726f8c0169b6
  React-RCTNetwork: 4650fca7376b98ed393626fb7c0fadc8c83b31fc
  React-RCTSettings: 6853d5f84a7a1c7e8ccc7991ef62edab607822aa
  React-RCTText: 7becec5f53f03b20da11f4b7e40e6bcfd476d134
  React-RCTVibration: e6c1b4ececa24ab574cfc628e2e8239c778eb4f1
  React-rncore: 0755299e60a7a6bb57cbe1836b38af09fad00759
  React-runtimeexecutor: 448409b5ae5a01b7793239f630051960c7dd39f9
  React-runtimescheduler: 975d835104d13603d49e15a6096c8a46ec92b4d4
  React-utils: 8929de4af0803247c15f8f38ec8ae5623b46f4d2
  ReactCommon: 7200e4235574a3c304acbd9eeac17f5f45dfd9db
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  RNCAsyncStorage: f47fe18526970a69c34b548883e1aeceb115e3e1
  RNDateTimePicker: 9b4091348e53f540180abdc54984d839a556f593
  RNFBApp: 1c8688e63b58bb57e6a6e9032aa227f8df8371bf
  RNFBAuth: f5ee3b29bcf9c4c53bca6352c1cd3d3f8a64838e
  RNGestureHandler: c0d04458598fcb26052494ae23dda8f8f5162b13
  RNKeychain: 7b805a511b820933a33cf7609c6333b792aaf645
  RNPermissions: 362e7751aff208ed7f8e802dc64498a7177db1ad
  RNScreens: 6a8a3c6b808aa48dca1780df7b73ea524f602c63
  RNVectorIcons: 29e429e12e75562ec41eb211b45a557240227f84
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  Stripe: cdf416cf2efe286f532a6306de0fcaa0ecc8c71a
  stripe-react-native: 55fbdae947bc2698d55193947fd17872d0eab786
  StripeApplePay: efb62ffc08e6cd4f161d77ddb45de2451075c54e
  StripeCore: 9731f05e327c3dcaf7d7abd116840ceaa9482bbe
  StripeFinancialConnections: 46c0049aaab3a179193502bce4a8096eb7b73f55
  StripePayments: dd1867a620b0b8b5e294e9ff2f1f7b7770765f47
  StripePaymentSheet: d155dfde74e90784d054deffb4f561a1f6dd638f
  StripePaymentsUI: c24f990b03a68a7f6fe704b15dd487e7bb6b603e
  StripeUICore: f2d514e900c37436dc5427fdf2c29d68ab1c2935
  TouchID: ba4c656d849cceabc2e4eef722dea5e55959ecf4
  TweetNacl: 3abf4d1d2082b0114e7a67410e300892448951e6
  Yoga: ef534101bb891fb09bae657417f34d399c1efe38

PODFILE CHECKSUM: f2e5e24a26714243444e416bc2f99ae38c7e55b6

COCOAPODS: 1.15.2
