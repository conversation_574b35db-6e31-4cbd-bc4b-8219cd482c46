import React, { useEffect, useMemo } from 'react';
import { ScrollView } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import { View, Dimensions, ActivityIndicator, ImageBackground } from 'react-native';
import Header from '../components/Header';
import CustomButton from '../components/CustomButton';
import JobApplicationCard from '../components/elements/JobApplicationCard';
import { BackgroundGradient } from '../assets/image';
import { useListBusinessApplicationsOfAJob } from '../services/jobsService';
import CustomText from '../components/CustomText';

export default function BusinessApplicationsOfAJob({ route, navigation }) {
  const { jobId, location, isNoti } = route.params || {};
  const { list, isFetching, reFetch } = useListBusinessApplicationsOfAJob(jobId);

  useEffect(() => {
    reFetch();
    // navigation on focus screen load user
    const unsubscribe = navigation.addListener('focus', () => {
      reFetch();
    });
    return () => {
      unsubscribe();
    };
  }, []);

  const styles = require('../../style/style');

  const backgroundStyle: any = {
    backgroundColor: Colors.black,
    width: '100%',
    height: '100%',
  };

  const buttonDetail: any = {
    marginTop: 25,
    alignSelf: 'center',
    borderRadius: 25,
    width: '80%',
  };
  const isEmpty = useMemo(() => !list || list.length === 0, [list]);

  return (
    <SafeAreaView style={backgroundStyle}>
      <ImageBackground source={BackgroundGradient} style={{ ...backgroundStyle, height: Dimensions.get('window').height }}>
        <Header title="Applications for the job" navigation={navigation} hasBack={!isNoti ? true : false} />
        <CustomButton
          style={buttonDetail}
          disabled={isFetching}
          onPress={() =>
            navigation.navigate('BusinessMyJobDetail', {
              jobId: jobId,
              hasApplications: !isEmpty,
            })
          }
          title={'View Job Details'}
          color={'white'}
        />
        <ScrollView style={styles.container}>
          <View>
            {isFetching ? (
              <View
                style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: 20,
                }}
              >
                <ActivityIndicator size="large" color={Colors.white} />
              </View>
            ) : (
              <View style={{ paddingTop: 10, gap: 10 }}>
                {isEmpty ? (
                  <CustomText text={'There are no applications for this Job'} color="white" />
                ) : (
                  <>
                    {list
                      .filter((item) => item.job_status[0] !== 'Hidden')
                      .map((item) => {
                        const email = item?.tradesperson_email;
                        const user_name = item?.tradesperson?.[0]?.user_name;
                        const qualifications = item?.tradesperson?.[0]?.qualifications;
                        const rateData = item?.tradesperson?.[0].rating;
                        return (
                          <JobApplicationCard
                            key={'application-' + item.id + item.date}
                            email={email}
                            name={user_name}
                            qualifications={qualifications}
                            style={{}}
                            rateData={rateData}
                            status={item.job_status[0]}
                            onTouch={() =>
                              navigation.navigate('Profile', {
                                isView: true,
                                isApprove: item.job_status[0] === 'Pending' ? true : false,
                                userId: item.tradesperson[0].ID,
                                jobId: jobId,
                                location: location,
                                applicationApproveId: item.id,
                              })
                            }
                          />
                        );
                      })}
                  </>
                )}
              </View>
            )}
          </View>
        </ScrollView>
      </ImageBackground>
    </SafeAreaView>
  );
}
