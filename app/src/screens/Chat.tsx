import React, { useCallback, useEffect, useState } from 'react';
import { View, SafeAreaView, Dimensions, ActivityIndicator, ImageBackground } from 'react-native';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import Header from '../components/Header';
import PageTitle from '../components/PageTitle';
import { GiftedChat } from 'react-native-gifted-chat';
import { getConversationDetail, readMessage, sendMessage } from '../services/messagesService';
import { useAuth } from '../../context/AuthContext';
const styles = require('../../style/style');
import { Pusher, PusherEvent } from '@pusher/pusher-websocket-react-native';
import { utils } from '../utils';
import moment from 'moment-timezone';
import { BackgroundGradient } from '../assets/image';
import { OneSignal } from 'react-native-onesignal';

const pusher = Pusher.getInstance();

const backgroundStyle = {
  backgroundColor: Colors.black,
  width: '100%',
  height: '100%',
};

const messageForm = {
  backgroundColor: Colors.white,
  marginTop: 0,
  borderRadius: 25,
  padding: 20,
  flexDirection: 'column',
  gap: 20,
  height: Dimensions.get('window').height - 200,
};

const Chat = ({ route, navigation }) => {
  const { conversationId, members, name, read_status } = route.params;
  const { authState } = useAuth();
  const userEmail = authState.user.email;
  const [messages, setMessages] = useState([]);
  const [uniqueEmailId, setUniqueEmailId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [conversation, setConversation] = useState(null);

  const fetchConversationDetail = async () => {
    setIsLoading(true);
    try {
      const conversationDetail = await getConversationDetail({
        ...(conversationId && { id: conversationId }),
        ...(members && { members: members }),
      });
      setConversation(conversationDetail);
      setIsLoading(false);
      setUniqueEmailId(conversationDetail?.unique_email_id);

      const dataMessages =
        conversationDetail?.chat_messages &&
        conversationDetail?.chat_messages?.reverse().map((message) => {
          return {
            _id: message.id,
            text: message.message,
            createdAt: new Date(moment.utc(message.post_date).local().format('YYYY-MM-DD HH:mm:ss')),
            user: {
              _id: message.sender_email === userEmail ? 1 : 2,
            },
          };
        });

      setMessages(dataMessages || []);
    } catch (error) {
      console.log('error', error);
      setIsLoading(false);
      utils.handleErrorNotify('Fail to fetch message');
    }
  };

  const connectPusher = async () => {
    await pusher.init({
      apiKey: '9258dcf2124012d284ac',
      cluster: 'eu',
    });

    await pusher.connect();
    await pusher.subscribe({
      channelName: 'conversation-' + conversationId,
      onEvent: (event: PusherEvent) => {
        const dataEvent = JSON.parse(event.data);
        if (dataEvent?.message?.sender_email === userEmail) {
          return;
        }
        setMessages((previousMessages) =>
          GiftedChat.append(previousMessages, [
            {
              _id: dataEvent?.message?.message_id,
              text: dataEvent?.message?.message,
              createdAt: new Date(),
              user: {
                _id: dataEvent?.message?.sender_email === userEmail ? 1 : 2,
              },
            },
          ])
        );
      },
    });
  };

  useEffect(() => {
    const markAsRead = async () => {
      if (conversationId) {
        try {
          await readMessage({ conversation_id: conversationId });
          // Clear notifications for this conversation
          OneSignal.Notifications.removeGroupedNotifications('Chat');
        } catch (error) {
          console.log('Error marking message as read:', error);
        }
      }
    };

    markAsRead();
    fetchConversationDetail();
    connectPusher();

    return () => {
      pusher.unsubscribe({ channelName: 'conversation-' + conversationId });
    };
  }, [conversationId]);

  const getEmailReceiver = (emails) => {
    return emails.filter((email) => email !== userEmail)[0];
  };

  const onSend = useCallback(
    async (messages = []) => {
      setMessages((previousMessages) => GiftedChat.append(previousMessages, messages));
      try {
        await sendMessage({
          conversation_id: conversation?.id,
          unique_email_id: uniqueEmailId,
          message: messages[0].text,
          receiver_email: getEmailReceiver(conversation?.user_emails),
        });
      } catch (error) {
        utils.handleErrorNotify('Fail to send message');
        setMessages((previousMessages) => previousMessages.slice(0, -1));
      }
    },
    [conversation, uniqueEmailId]
  );

  return (
    <SafeAreaView style={backgroundStyle}>
      <ImageBackground source={BackgroundGradient} style={{ ...backgroundStyle, height: Dimensions.get('window').height }}>
        <Header title="Message" navigation={navigation} hasBack />
        <View style={styles.container}>
          <View style={messageForm}>
            <View style={{ flexDirection: 'row' }}>
              <View style={{ flex: 1, alignItems: 'center' }}>
                <PageTitle title={name} style={{ color: Colors.black }} />
              </View>
            </View>
            {isLoading ? (
              <View style={styles.loading}>
                <ActivityIndicator size="large" color={Colors.black} />
              </View>
            ) : (
              <GiftedChat
                messages={messages}
                onSend={(messages) =>
                  onSend(
                    messages.map((message) => {
                      return {
                        ...message,
                        createdAt: new Date(moment.tz(message.createdAt, 'Europe/London').format('YYYY-MM-DD HH:mm:ss')),
                      };
                    })
                  )
                }
                user={{
                  _id: 1,
                }}
                renderAvatar={null}
              />
            )}
          </View>
        </View>
      </ImageBackground>
    </SafeAreaView>
  );
};

export default Chat;
