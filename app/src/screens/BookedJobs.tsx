import React, { useEffect, useState } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import { View, Text, ImageBackground, Dimensions, Alert, Platform, ActivityIndicator } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import Header from '../components/Header';
import { BackgroundGradient } from '../assets/image';
import { useListTradespersonApplications } from '../services/jobsService';
import moment from 'moment-timezone';
import * as AddCalendarEvent from 'react-native-add-calendar-event';
import { PERMISSIONS, RESULTS, request } from 'react-native-permissions';
import CustomButton from '../components/CustomButton';

export default function BookedJobs({ navigation }) {
  const { list: rawlist, isFetching } = useListTradespersonApplications();
  const [bookedJobs, setBookedJobs] = useState([]);

  useEffect(() => {
    if (rawlist) {
      const approvedJobs = rawlist
        .filter((item) => item.job_status[0] === 'Approved')
        .map((item) => ({
          ...item,
          job: item.job[0],
          companyName: item.business[0]?.user_name || 'Unknown Company',
        }))
        .sort((a, b) => moment(a.job?.start_date_required).diff(moment(b.job?.start_date_required)));
      setBookedJobs(approvedJobs);
    }
  }, [rawlist]);

  const handleAddToCalendar = async (job) => {
    try {
      const permissionWriteOnly = await request(Platform.OS === 'ios' ? PERMISSIONS.IOS.CALENDARS_WRITE_ONLY : PERMISSIONS.ANDROID.WRITE_CALENDAR);
      const permissionFull = await request(Platform.OS === 'ios' ? PERMISSIONS.IOS.CALENDARS : PERMISSIONS.ANDROID.WRITE_CALENDAR);

      if (permissionWriteOnly !== RESULTS.GRANTED && permissionFull !== RESULTS.GRANTED) {
        Alert.alert('Permission required', 'Please enable calendar permission in settings');
        return;
      }

      const eventConfig = {
        title: `TM ${job.job?.post_title}`,
        location: job.job?.location || 'No location provided',
        startDate: new Date(job.job?.start_date_required).toISOString(),
        endDate: new Date(job.job?.start_date_required).toISOString(),
        notes: 'You have a job reminder today!',
        navigationBarIOS: {
          barTintColor: 'orange',
          tintColor: 'green',
          translucent: false,
          backgroundColor: 'green',
          titleColor: 'blue',
        },
      };

      const eventId = await AddCalendarEvent.presentEventCreatingDialog(eventConfig);
      if (eventId) {
        Alert.alert('Success', 'Job added to calendar');
      }
    } catch (error) {
      console.log('handleAddToCalendar error', error);
      Alert.alert('Error', 'Could not add to calendar.');
    }
  };

  const groupJobsByDate = () => {
    const grouped = {};
    bookedJobs.forEach((job) => {
      const date = moment(job.job?.start_date_required).format('YYYY-MM-DD');
      if (!grouped[date]) {
        grouped[date] = [];
      }
      grouped[date].push(job);
    });
    return grouped;
  };

  return (
    <SafeAreaView style={{ backgroundColor: Colors.black, width: '100%', height: '100%' }}>
      <ImageBackground source={BackgroundGradient} style={{ height: Dimensions.get('window').height }}>
        <Header title="Booked Jobs Calendar" navigation={navigation} hasBack />
        <ScrollView style={{ padding: 15 }}>
          {isFetching ? (
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', marginTop: 50 }}>
              <ActivityIndicator size="large" color={Colors.white} />
              <Text style={{ color: Colors.white, marginTop: 10 }}>Loading booked jobs...</Text>
            </View>
          ) : (
            <>
              {Object.entries(groupJobsByDate()).map(([date, jobs]: [string, any[]]) => (
                <View key={date} style={{ marginBottom: 20 }}>
                  <Text
                    style={{
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: 'bold',
                      marginBottom: 10,
                      backgroundColor: 'rgba(0,0,0,0.5)',
                      padding: 10,
                      borderRadius: 5,
                    }}
                  >
                    {moment(date).format('dddd, MMMM D, YYYY')}
                  </Text>
                  {jobs.map((job: any, index: number) => (
                    <View
                      key={index}
                      style={{
                        backgroundColor: Colors.white,
                        borderRadius: 10,
                        padding: 15,
                        marginBottom: 10,
                      }}
                    >
                      <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 5 }}>{job.job?.post_title}</Text>
                      <Text style={{ marginBottom: 5 }}>Company: {job.companyName}</Text>
                      <Text style={{ marginBottom: 5 }}>Time: {moment(job.job?.start_date_required).format('h:mm A')}</Text>
                      <Text style={{ marginBottom: 10 }}>Location: {job.job?.location}</Text>
                      <CustomButton style={{ borderRadius: 5 }} title="Add to Calendar" color="black" onPress={() => handleAddToCalendar(job)} />
                    </View>
                  ))}
                </View>
              ))}
              {bookedJobs.length === 0 && !isFetching && (
                <View
                  style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginTop: 50,
                  }}
                >
                  <Text style={{ color: Colors.white, fontSize: 16 }}>No booked jobs found</Text>
                </View>
              )}
            </>
          )}
        </ScrollView>
      </ImageBackground>
    </SafeAreaView>
  );
}
