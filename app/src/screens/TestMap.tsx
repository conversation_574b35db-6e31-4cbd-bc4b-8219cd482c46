import React, { useEffect, useRef } from 'react';
import { View, Alert, Text } from 'react-native';
import MapView, { Marker, Callout } from 'react-native-maps';

const TestMap = () => {
    const handlePress = () => {
        Alert.alert('Marker Pressed');
    };

    useEffect(() => {
        markerRef.current?.showCallout();
    }, []);

    const markerRef = useRef(null);

    return (
        <View style={{ flex: 1 }}>
            <MapView
                initialRegion={{
                    latitude: 51.5285257,
                    longitude: -0.2667455,
                    latitudeDelta: 0.0922,
                    longitudeDelta: 0.0421,
                }}
                style={{
                    flex: 1,
                }}
                provider='google'
            >
                <Marker
                    coordinate={{
                        latitude: 51.5285257,
                        longitude: -0.2667455,
                    }}
                    ref={markerRef}
                >
                    <Callout onPress={handlePress}>
                        <View style={{ padding: 20 }}>
                            <Text>Demo Job</Text>
                        </View>
                    </Callout>
                </Marker>
            </MapView>
        </View>
    );
};

export default TestMap;