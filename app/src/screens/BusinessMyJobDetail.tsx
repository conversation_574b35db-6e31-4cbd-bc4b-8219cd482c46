import React, { useEffect } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import Header from '../components/Header';

import { View, Alert, ViewStyle, TextInput, ActivityIndicator, Text, ImageBackground, Dimensions } from 'react-native';
import CustomText from '../components/CustomText';
import CustomTextInput from '../components/CustomTextInput';
import CustomButton from '../components/CustomButton';
import BouncyCheckbox from 'react-native-bouncy-checkbox';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Controller, useForm } from 'react-hook-form';
import { useAuth } from '../../context/AuthContext';
import { getJobById, updateJob } from '../services/jobsService';
import KeyboardAwareScrollViewCustom from '../components/elements/KeyboardAwareScrollViewCustom';
import moment from 'moment-timezone';
import Checkbox from '../components/elements/Checkbox';
import { BackgroundGradient } from '../assets/image';
import { getTradeOptions } from '../constants/tradeOptions';
import DropdownComponent from '../components/elements/Dropdown';
const DateTimePickerStyle: ViewStyle = {
  alignItems: 'flex-start',
  width: '100%',
};
function getDiffRequirements(requirements, requirementsOptions) {
  return requirements.filter((req) => !requirementsOptions.includes(req));
}
const requirementsOptions = [
  'Public Liability Insurance (Minimum £5m)',
  'Trade Specific CSCS Card or Equivalent',
  'UK Bank Account',
  'Personal Protective Equipment (PPE) (See application for specific requirements)',
];

const tradeOptions = getTradeOptions({ excluded: ['All'] });

export default function BusinessMyJobDetail({ route, navigation }) {
  const jobId = route.params?.jobId;
  const hasApplications = route.params?.hasApplications;
  const styles = require('../../style/style');
  const { authState, loadUser } = useAuth();
  const [isLoading, setIsLoading] = React.useState(false);
  const [isLoadingGetData, setIsLoadingGetData] = React.useState(false);

  const [jobData, setJobData] = React.useState(null);
  const [selectedStartDate, setSelectedStartDate] = React.useState(moment().toISOString());
  const [isPoa, setIsPoa] = React.useState(false);

  const [isOther, setIsOther] = React.useState(false);
  const [otherInput, setOtherInput] = React.useState('');
  const [priceForm, setPriceForm] = React.useState('full');

  const { getValues, control, setValue, watch } = useForm({
    defaultValues: {
      location: '',
      short_location: '',
      task: '',
      price: '',
      start_date_required: '',
      arrival_date_required: '',
      requirements: [],
      trade: [],
      post_code: '',
      contact_name: '',
      site_name: '',
    },
  });

  useEffect(() => {
    if (!jobId) return;
    setIsLoadingGetData(true);
    getJobById(jobId).then((res) => {
      const job = res.data;
      setIsLoadingGetData(false);
      setJobData(job);
    });
  }, [jobId, setValue]);

  useEffect(() => {
    if (jobData) {
      setValue('location', jobData.location);
      setValue('short_location', jobData.short_location);
      setValue('task', jobData.task);
      setValue('price', jobData.price === 'poa' ? '' : jobData.price);
      setSelectedStartDate(moment.utc(jobData.start_date_required).local().toISOString());
      setValue('requirements', jobData.requirements);
      setValue('trade', jobData.trade);
      if (jobData.price === 'poa') {
        setIsPoa(true);
      }
      setValue('post_code', jobData.post_code);
      setValue('contact_name', jobData.contact_name);
      setValue('site_name', jobData.site_name);
      if (jobData.price.includes('Per hour')) {
        setValue('price', jobData.price.replace('Per hour', ''));
        setPriceForm('per_hour');
      }
      if (jobData.requirements) {
        const diffRequirements = getDiffRequirements(jobData.requirements, requirementsOptions);
        if (diffRequirements.length > 0) {
          setIsOther(true);
          setOtherInput(diffRequirements[0]);
        } else {
          setIsOther(false);
          setOtherInput('');
        }
      }
    }
  }, [jobData, setValue]);

  const values = getValues();

  const backgroundStyle: ViewStyle = {
    backgroundColor: Colors.black,
    width: '100%',
    height: '100%',
  };

  const jobForm: ViewStyle = {
    backgroundColor: Colors.white,
    marginTop: 16,
    borderRadius: 25,
    padding: 20,
    flexDirection: 'column',
    gap: 20,
  };

  const row: ViewStyle = {
    flexDirection: 'row',
  };

  const requirementsStyle: ViewStyle = {
    margin: 5,
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    const values = getValues();
    if (!values.location || !values.task || (!isPoa && !values.price) || !values.requirements || !selectedStartDate) {
      Alert.alert('Please fill in all the fields');
      setIsLoading(false);
      return;
    }
    const filteredRequirements = values.requirements.filter((req) => requirementsOptions.includes(req));
    if (otherInput && !requirementsOptions.includes(otherInput)) {
      filteredRequirements.push(otherInput);
    }
    try {
      const data = await updateJob(jobId, {
        start_date_required: selectedStartDate,
        location: values.location,
        short_location: values.short_location,
        post_code: values.post_code,
        contact_name: values.contact_name,
        site_name: values.site_name,
        price: values.price + (priceForm === 'per_hour' ? ' Per hour' : ''),
        requirements: filteredRequirements,
        task: values.task,
        post_title: values.task,
        user_post: authState.user?.id,
        trade: values.trade,
      });

      if (data && data.success) {
        await loadUser();
        Alert.alert('update job successfully');
      } else {
        Alert.alert('updated job failed: ' + data);
      }
    } catch (err) {
      Alert.alert('updated job failed: ' + String(err));
    }
    setIsLoading(false);
  };
  const requirements = watch('requirements');

  return (
    <SafeAreaView style={backgroundStyle}>
      <ImageBackground source={BackgroundGradient} style={{ ...backgroundStyle, height: Dimensions.get('window').height }}>
        <Header title={hasApplications ? 'Job Details' : 'Edit Job Detail'} navigation={navigation} hasBack />
        <KeyboardAwareScrollViewCustom isEnableScroll>
          <View style={[styles.container, { marginBottom: 100 }]}>
            {!jobId ? (
              <CustomText text="Job not found" color="white" />
            ) : (
              <>
                {isLoadingGetData ? (
                  <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                    <ActivityIndicator size="large" color={Colors.white} />
                  </View>
                ) : (
                  <View style={jobForm}>
                    <View>
                      <CustomText text="Trade" />
                      <DropdownComponent
                        value={values.trade[0]}
                        data={tradeOptions}
                        onSelectItem={(item) => {
                          setValue('trade', [item]);
                        }}
                        placeholder={'Select Trades'}
                        disabled={hasApplications}
                      />
                    </View>
                    <View>
                      <CustomText text="Location (Town/City):" />
                      <CustomTextInput isLoading={isLoading} control={control} name="location" isDisabled={hasApplications} />
                    </View>
                    <View>
                      <CustomText text="Full post code of job location (issued on approval):" />
                      <CustomTextInput isLoading={isLoading} control={control} name="post_code" isDisabled={hasApplications} />
                    </View>

                    <View>
                      <CustomText text="Job contact name (issued on approval):" />
                      <CustomTextInput isLoading={isLoading} control={control} name="contact_name" isDisabled={hasApplications} />
                    </View>

                    <View>
                      <CustomText text="Site name (issued on approval):" />
                      <CustomTextInput isLoading={isLoading} control={control} name="site_name" isDisabled={hasApplications} />
                    </View>

                    <View>
                      <CustomText text="Fix/Task/Job description:" />
                      <CustomTextInput
                        multiline
                        isLoading={isLoading}
                        inputStyle={{ height: 90 }}
                        control={control}
                        name="task"
                        isDisabled={hasApplications}
                      />
                    </View>

                    <View style={{ display: 'flex' }}>
                      <CustomText text="Price:" />
                      <View style={{ flexDirection: 'row', alignItems: 'center', width: '100%' }}>
                        <CustomTextInput
                          isLoading={isLoading}
                          isDisabled={priceForm === 'poa' || hasApplications}
                          control={control}
                          name="price"
                          inputStyle={{ paddingLeft: 30, minWidth: 180, width: '60%' }}
                          isNumber
                          prefix={priceForm !== 'poa' && <Text style={{ fontSize: 20, paddingTop: 18, marginRight: 15 }}>£</Text>}
                          suffix={priceForm === 'per_hour' && <Text style={{ fontSize: 16, paddingTop: 18 }}>(Per Hour)</Text>}
                        />
                        <View style={{ flexDirection: 'column', alignItems: 'flex-start', marginLeft: 18 }}>
                          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <Checkbox
                              disabled={hasApplications}
                              isChecked={priceForm === 'full'}
                              onPress={() => {
                                if (getValues('price') === 'poa') {
                                  setValue('price', '0');
                                }
                                setPriceForm('full');
                              }}
                            />
                            <Text style={{ fontSize: 18 }}>FULL PRICE</Text>
                          </View>
                          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <Checkbox
                              disabled={hasApplications}
                              isChecked={priceForm === 'per_hour'}
                              onPress={() => {
                                if (getValues('price') === 'poa') {
                                  setValue('price', '0');
                                }
                                setPriceForm('per_hour');
                              }}
                            />
                            <Text style={{ fontSize: 18 }}>PER HOUR</Text>
                          </View>
                          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <Checkbox
                              disabled={hasApplications}
                              isChecked={priceForm === 'poa'}
                              onPress={() => {
                                setValue('price', 'poa');
                                setPriceForm('poa');
                              }}
                            />
                            <Text style={{ fontSize: 18 }}>POA</Text>
                          </View>
                        </View>
                      </View>
                    </View>
                    <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
                      <CustomText text="Start date required:" style={{ width: 160 }} />
                      <Controller
                        control={control}
                        name={'start_date_required'}
                        defaultValue={selectedStartDate}
                        render={() => (
                          <View style={DateTimePickerStyle}>
                            <DateTimePicker
                              disabled={isLoading || hasApplications}
                              value={selectedStartDate ? new Date(selectedStartDate) : new Date()}
                              mode="date"
                              display="default"
                              onChange={(event, date) => {
                                setSelectedStartDate(moment(date).toISOString());
                              }}
                              themeVariant="light"
                              accentColor="black"
                            />
                          </View>
                        )}
                      />
                    </View>
                    <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
                      <CustomText text="Arrival time required:" style={{ width: 160 }} />
                      <Controller
                        control={control}
                        name={'start_date_required'}
                        defaultValue={selectedStartDate}
                        render={() => (
                          <View style={DateTimePickerStyle}>
                            <DateTimePicker
                              disabled={isLoading || hasApplications}
                              value={selectedStartDate ? new Date(selectedStartDate) : new Date()}
                              mode="time"
                              display="default"
                              onChange={(event, date) => {
                                setSelectedStartDate(moment(date).toISOString());
                              }}
                              themeVariant="light"
                              accentColor="black"
                            />
                          </View>
                        )}
                      />
                    </View>
                    {requirements ? (
                      <>
                        <View>
                          <CustomText text="Requirements:" style={{ marginBottom: 5 }} />
                          {requirementsOptions.map((option) => (
                            <View key={option} style={{ ...row, ...requirementsStyle }}>
                              <BouncyCheckbox
                                key={requirements.includes(option) ? 'checked' : 'unchecked'}
                                size={20}
                                disabled={isLoading || hasApplications}
                                fillColor={Colors.black}
                                unfillColor={Colors.white}
                                iconStyle={{ borderColor: Colors.gray }}
                                innerIconStyle={{
                                  borderWidth: 1,
                                  borderColor: Colors.black,
                                }}
                                isChecked={requirements.includes(option)}
                                onPress={(isChecked) => {
                                  const newRequirements = isChecked ? [...requirements, option] : requirements.filter((item) => item !== option);
                                  setValue('requirements', newRequirements);
                                }}
                              />
                              <CustomText text={option} style={{ marginRight: 15 }} />
                            </View>
                          ))}
                          <Controller control={control} render={() => null} name="requirements" defaultValue={values.requirements} />
                        </View>
                        <View style={{ flexDirection: 'column', margin: 5, marginTop: -15 }}>
                          <View style={{ ...row }}>
                            <BouncyCheckbox
                              key={isOther ? 'checked' : 'unchecked'}
                              size={20}
                              disabled={isLoading || hasApplications}
                              fillColor={Colors.black}
                              unfillColor={Colors.white}
                              iconStyle={{ borderColor: Colors.gray }}
                              innerIconStyle={{
                                borderWidth: 1,
                                borderColor: Colors.black,
                              }}
                              isChecked={isOther}
                              onPress={(isChecked) => {
                                if (isChecked) {
                                  setIsOther(true);
                                } else {
                                  setOtherInput('');
                                  setIsOther(false);
                                }
                              }}
                            />
                            <CustomText text="Other" />
                          </View>
                          {isOther && (
                            <View>
                              <TextInput
                                disableFullscreenUI={isLoading || hasApplications}
                                autoCapitalize="none"
                                autoFocus={false}
                                editable={!hasApplications}
                                style={{
                                  borderWidth: 1,
                                  borderColor: Colors.gray,
                                  marginTop: 10,
                                  height: 40,
                                  paddingHorizontal: 10,
                                  fontFamily: 'Montserrat-Regular',
                                }}
                                value={otherInput}
                                blurOnSubmit={false}
                                onChangeText={setOtherInput}
                              />
                            </View>
                          )}
                        </View>
                      </>
                    ) : null}
                    {!hasApplications && (
                      <CustomButton
                        navigation={navigation}
                        title={isLoading ? 'Updating Job..' : 'Update Job'}
                        isLoading={isLoading}
                        to=""
                        onPress={() => handleSubmit()}
                        color="black"
                      />
                    )}
                  </View>
                )}
              </>
            )}
          </View>
        </KeyboardAwareScrollViewCustom>
      </ImageBackground>
    </SafeAreaView>
  );
}
