import React, { useState, useEffect } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import { ActivityIndicator, Image, StyleSheet, View, ImageBackground, Dimensions, Alert } from 'react-native';
import { useForm } from 'react-hook-form';
import { BackgroundGradient, Logo } from '../assets/image';
import CustomButton from '../components/CustomButton';
import CustomText from '../components/CustomText';
import CustomTextInput from '../components/CustomTextInput';
import ForgotPasswordModal from '../components/ForgotPasswordModal';
import { utils } from '../utils';
import { useAuth } from '../../context/AuthContext';
import packageJson from '../../package.json';
import KeyboardAwareScrollViewCustom from '../components/elements/KeyboardAwareScrollViewCustom';
import TouchID from 'react-native-touch-id';
import KeychainService from '../services/KeychainService';

const baseStyles = require('../../style/style');

function Login({ route, navigation }: any) {
  const { onLogin, authState } = useAuth();
  const { control, getValues, handleSubmit, setFocus, register, setValue } = useForm();
  const [loading, setLoading] = useState(false);
  const [loadingLoginByToken, setLoadingLoginByToken] = useState(false);
  const [loadingKeychainLogin, setLoadingKeychainLogin] = useState(false);
  const [biometricAvailable, setBiometricAvailable] = useState(false);
  const [hasSavedCredentials, setHasSavedCredentials] = useState(false);
  const [showForgotPasswordModal, setShowForgotPasswordModal] = useState(false);
  const [autoFilledCredentials, setAutoFilledCredentials] = useState(false);

  // Initial checks on mount
  useEffect(() => {
    console.log('Login component mounted');
    checkBiometricAvailability();
    checkSavedCredentials();
  }, []);

  // // Run this whenever the component gets focus
  // useEffect(() => {
  //   const unsubscribe = navigation.addListener('focus', () => {
  //     console.log('Login screen focused - checking for saved credentials');
  //     checkSavedCredentials();
  //   });

  //   return unsubscribe;
  // }, [navigation]);

  const checkBiometricAvailability = async () => {
    try {
      const biometryType = await TouchID.isSupported();
      setBiometricAvailable(!!biometryType);
    } catch (error) {
      console.log('TouchID error:', error);
      setBiometricAvailable(false);
    }
  };

  const checkSavedCredentials = async () => {
    try {
      const credentials = await KeychainService.getCredentials();
      console.log('Retrieved credentials directly:', credentials ? 'found' : 'not found');

      const hasCredentials = await KeychainService.hasCredentials();
      console.log('checkSavedCredentials result:', hasCredentials);

      if (hasCredentials !== !!credentials) {
        console.log('WARNING: Inconsistency in credential check');
      }

      setHasSavedCredentials(hasCredentials);

      if (hasCredentials && credentials) {
        console.log('Auto-filling email for user:', credentials.username);
        setValue('email', credentials.username);
        setAutoFilledCredentials(true);
      } else {
        setAutoFilledCredentials(false);
      }
    } catch (error) {
      console.log('Keychain error:', error);
      setHasSavedCredentials(false);
      setAutoFilledCredentials(false);
    }
  };

  const askToSaveCredentials = (email: string, password: string) => {
    Alert.alert('Save Credentials', 'Would you like to save your login information for future use?', [
      {
        text: 'No',
        style: 'cancel',
      },
      {
        text: 'Yes',
        onPress: async () => {
          console.log('User agreed to save credentials');
          const saved = await KeychainService.storeCredentials(email, password);
          console.log('Credentials save result:', saved);
          if (saved) {
            await checkSavedCredentials();
            console.log('Updated hasSavedCredentials:', hasSavedCredentials);
          }
        },
      },
    ]);
  };

  const login = async () => {
    try {
      setLoading(true);
      const { email, password } = getValues();

      console.log('Attempting login with:', email);
      const res: any = await onLogin(email, password);

      if (res?.data?.success) {
        const destination = (route?.params && route.params?.screen) || 'Home';
        const params = route.params?.screen ? route?.params?.params : undefined;
        navigation.navigate(destination, params);
      } else if (res.msg) {
        let errorMessage = res.msg;
        if (typeof res.msg === 'string' && res.msg.includes('[') && res.msg.includes(']')) {
          errorMessage = res.msg.split(']')[1].trim();
        }
        utils.handleErrorNotify(errorMessage);
      }
    } catch (error: any) {
      utils.handleErrorNotify(error);
    } finally {
      setLoading(false);
    }
  };

  const handleFaceID = async () => {
    setLoadingLoginByToken(true);
    try {
      const credentials = await KeychainService.getCredentials();
      if (!credentials) {
        utils.handleErrorNotify('You need to login using password before using FaceID/TouchID to login...');
        setLoadingLoginByToken(false);
        return;
      }

      const biometricResult = await TouchID.authenticate('Login with FaceID/TouchID', {});
      if (biometricResult) {
        const res: any = await onLogin(credentials.username, credentials.password);

        if (res?.data?.success) {
          const destination = (route?.params && route.params?.screen) || 'Home';
          const params = route.params?.screen ? route?.params?.params : undefined;
          navigation.navigate(destination, params);
        } else if (res.msg) {
          let errorMessage = res.msg;
          if (typeof res.msg === 'string' && res.msg.includes('[') && res.msg.includes(']')) {
            errorMessage = res.msg.split(']')[1].trim();
          }
          utils.handleErrorNotify(errorMessage);
        }
      }
    } catch (error) {
      console.log('TouchID Error: ', error);
    } finally {
      setLoadingLoginByToken(false);
    }
  };

  const handleKeychainLogin = async () => {
    setLoadingKeychainLogin(true);
    try {
      const credentials = await KeychainService.getCredentials();
      console.log('Keychain login with credentials:', credentials ? 'found' : 'not found');
      if (!credentials) {
        utils.handleErrorNotify('No saved credentials found. Please login with your email and password first.');
        setLoadingKeychainLogin(false);
        return;
      }

      // Require FaceID/TouchID authentication
      try {
        console.log('Requesting biometric authentication for saved credentials');
        const biometricResult = await TouchID.authenticate('Authenticate to use saved credentials', {});
        if (!biometricResult) {
          utils.handleErrorNotify('Authentication failed. Please try again.');
          setLoadingKeychainLogin(false);
          return;
        }
      } catch (biometricError) {
        console.log('Biometric authentication error:', biometricError);
        utils.handleErrorNotify('Authentication cancelled or failed. Please try again.');
        setLoadingKeychainLogin(false);
        return;
      }

      const res: any = await onLogin(credentials.username, credentials.password);

      if (res?.data?.success) {
        const destination = (route?.params && route.params?.screen) || 'Home';
        const params = route.params?.screen ? route?.params?.params : undefined;
        navigation.navigate(destination, params);
      } else if (res.msg) {
        let errorMessage = res.msg;
        if (typeof res.msg === 'string' && res.msg.includes('[') && res.msg.includes(']')) {
          errorMessage = res.msg.split(']')[1].trim();
        }
        utils.handleErrorNotify(errorMessage);
      }
    } catch (error) {
      console.log('Keychain login error:', error);
      utils.handleErrorNotify('Failed to login with saved credentials.');
    } finally {
      setLoadingKeychainLogin(false);
    }
  };

  // Log current state of credentials
  useEffect(() => {
    console.log('Current hasSavedCredentials state:', hasSavedCredentials);
  }, [hasSavedCredentials]);

  return (
    <SafeAreaView
      style={{
        ...styles.backgroundStyle,
        flex: 1,
        justifyContent: 'center',
      }}
    >
      <ImageBackground
        source={BackgroundGradient}
        style={{
          ...styles.backgroundStyle,
          flex: 1,
          justifyContent: 'center',
          height: Dimensions.get('window').height,
        }}
      >
        <View>
          <KeyboardAwareScrollViewCustom>
            <View style={{ ...baseStyles.container, marginTop: 0 }}>
              <View style={styles.imageContainer}>
                <Image source={Logo} style={styles.imageStyle} />
              </View>
              <View style={styles.formContainer}>
                <CustomText text={`Welcome to Trade Motion (v${packageJson.version})`} isBold={false} position="center" />
                {!authState.didInitialFetch ? (
                  <View
                    style={{
                      width: '100%',
                      height: 100,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <ActivityIndicator size="large" color={'black'} />
                  </View>
                ) : (
                  <View>
                    <CustomText text="Email Address" />
                    <CustomTextInput
                      name="email"
                      control={control}
                      register={register}
                      inputRest={{
                        returnKeyType: 'next',
                        onSubmitEditing: () => setFocus('password'),
                      }}
                      rules={{
                        required: 'This field is required',
                      }}
                    />
                    {/* Hidden: Saved credentials indicator */}
                    <CustomText text="Password" style={{ marginTop: 10 }} />
                    <CustomTextInput
                      name="password"
                      register={register}
                      control={control}
                      rules={{
                        required: 'This field is required',
                      }}
                      inputRest={{
                        secureTextEntry: true,
                        returnKeyType: 'go',
                        onSubmitEditing: () => handleSubmit(login)(),
                        placeholder: 'Enter your password',
                      }}
                    />
                    <CustomText
                      text="Forgot Password?"
                      isUnderline={true}
                      position="right"
                      style={{ marginTop: 8, fontSize: 12 }}
                      onPress={() => setShowForgotPasswordModal(true)}
                    />
                  </View>
                )}
                <CustomButton
                  title={!loading ? 'Login Here' : 'Logging in..'}
                  color="black"
                  isLoading={loading}
                  onPress={() => handleSubmit(login)()}
                />
                {/* Temporarily hidden - Login with saved credentials
                {hasSavedCredentials && (
                  <CustomButton
                    title="Login with Saved Credentials"
                    color="black"
                    isLoading={loadingKeychainLogin}
                    onPress={() => handleKeychainLogin()}
                  />
                )} */}
                {biometricAvailable && (
                  <CustomButton title="FaceID/TouchID" color="black" isLoading={loadingLoginByToken} onPress={() => handleFaceID()} />
                )}
                <CustomText
                  text="New to TradeMotion? Register now"
                  isUnderline={true}
                  position="center"
                  onPress={() => navigation.navigate('Register')}
                />
              </View>
            </View>
          </KeyboardAwareScrollViewCustom>
        </View>
      </ImageBackground>

      <ForgotPasswordModal visible={showForgotPasswordModal} onClose={() => setShowForgotPasswordModal(false)} />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  backgroundStyle: {
    backgroundColor: Colors.black,
    width: '100%',
    height: '100%',
  },
  imageContainer: {
    width: '100%',
    height: 200,
  },
  imageStyle: {
    flex: 1,
    width: null,
    height: null,
    resizeMode: 'contain',
  },
  formContainer: {
    backgroundColor: Colors.white,
    marginTop: 56,
    borderRadius: 50,
    padding: 20,
    flexDirection: 'column',
    gap: 20,
  },
});

export default Login;
