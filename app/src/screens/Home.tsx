import React, { useEffect, useState } from 'react';

import { ScrollView } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import Header from '../components/Header';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import { View, Dimensions, ImageBackground } from 'react-native';
import PageTitle from '../components/PageTitle';
import { Email, Plus, News, Logout, List, User, CheckList, BackgroundGradient, Calendar } from '../assets/image';
import GridButton from '../components/GridButton';
import { useAuth } from '../../context/AuthContext';
import { getHomeGridButtonNumber } from '../services/jobsService';
import { useAtom } from 'jotai';
import { buttonNumbersAtom } from '../atoms/buttonNumbersAtom';

export default function Home({ route, navigation }) {
  const { authState } = useAuth();

  const [buttonNumbers, setButtonNumbers] = useAtom(buttonNumbersAtom);

  const updateGridNumber = async () => {
    try {
      const res = await getHomeGridButtonNumber();
      if (res.success) {
        setButtonNumbers({
          applications: res.data.jobApplication,
          currentJobs: res.data.currentJobs,
          profile: res.data.profile,
          messages: res.data.messages,
          news: res.data.news,
        });
      }
    } catch (err) {
      console.error({ err });
    }
  };

  useEffect(() => {
    updateGridNumber();

    const unsubscribe = navigation.addListener('focus', () => {
      updateGridNumber();
    });

    return unsubscribe;
  }, []);

  useEffect(() => {
    if (!authState.token) {
      console.log('route?.params', route?.params);

      if (route?.params && route?.params?.screen !== '') {
        navigation.navigate('Login', {
          screen: route.params.screen,
          params: route.params.params,
        });
      } else {
        navigation.navigate('Login');
      }
    } else {
      if (route?.params && route.params?.screen !== '') {
        console.log('route?.params 2', route?.params);
        navigation.navigate(route.params.screen, route.params.params);
      }

      updateGridNumber();
    }
  }, [authState, navigation, route.params, setButtonNumbers]);

  const isTrade = authState.isTrade;
  const styles = require('../../style/style');

  const backgroundStyle: any = {
    backgroundColor: Colors.black,
    width: '100%',
    height: '100%',
  };

  const grid: any = {
    flexDirection: 'row',
    marginTop: 20,
    gap: 15,
    flexWrap: 'wrap',
  };

  return (
    <SafeAreaView style={backgroundStyle}>
      <ImageBackground source={BackgroundGradient} style={{ ...backgroundStyle, height: Dimensions.get('window').height }}>
        <ScrollView>
          <Header isHome navigation={navigation} />
          <View style={styles.container}>
            {/* <View style={homeContainer}> */}
            <PageTitle style={{ marginBottom: 10, alignSelf: 'center', fontSize: 25 }} title="Home"></PageTitle>
            <View style={grid}>
              {isTrade ? (
                <>
                  <GridButton navigation={navigation} backgroundIcon="#32C4F7" source={List} to="FindJob" text="Available Jobs" trade={isTrade} />
                  <GridButton
                    navigation={navigation}
                    backgroundIcon="#F96747"
                    source={CheckList}
                    to="Applications"
                    text="Applications"
                    trade={isTrade}
                    hasRedDot={buttonNumbers.applications > 0 ? true : false}
                    numberInRedDot={buttonNumbers.applications}
                  />
                  <GridButton
                    navigation={navigation}
                    backgroundIcon="#EE82EE"
                    source={User}
                    to="Profile"
                    text="Bio"
                    trade={isTrade}
                    hasRedDot={buttonNumbers.profile > 0 ? true : false}
                    numberInRedDot={buttonNumbers.profile}
                  />
                  <GridButton
                    navigation={navigation}
                    backgroundIcon="#4689D6"
                    source={Email}
                    to="Messages"
                    text="Messages"
                    trade={isTrade}
                    hasRedDot={buttonNumbers.messages > 0 ? true : false}
                    numberInRedDot={buttonNumbers.messages}
                  />
                  <GridButton
                    navigation={navigation}
                    backgroundIcon="#FFA500"
                    source={News}
                    to="Newsflash"
                    text="News"
                    trade={isTrade}
                    hasRedDot={buttonNumbers.news > 0 ? true : false}
                    numberInRedDot={buttonNumbers.news}
                  />
                  <GridButton navigation={navigation} backgroundIcon="#4CAF50" source={Calendar} to="BookedJobs" text="Calendar" trade={isTrade} />
                </>
              ) : (
                <>
                  <GridButton navigation={navigation} backgroundIcon="#9C7FDE" source={Plus} to="PostJob" text="Post a Job" trade={isTrade} />
                  <GridButton
                    navigation={navigation}
                    backgroundIcon="#31BF9A"
                    source={List}
                    to="CurrentPost"
                    text="Current Jobs"
                    trade={isTrade}
                    hasRedDot={buttonNumbers.currentJobs > 0 ? true : false}
                    numberInRedDot={buttonNumbers.currentJobs}
                  />
                  <GridButton
                    navigation={navigation}
                    backgroundIcon="#FCBA00"
                    source={User}
                    to="Profile"
                    text="Profile"
                    trade={isTrade}
                    hasRedDot={buttonNumbers.profile > 0 ? true : false}
                    numberInRedDot={buttonNumbers.profile}
                  />
                  <GridButton
                    navigation={navigation}
                    backgroundIcon="#4689D6"
                    source={Email}
                    to="Messages"
                    text="Messages"
                    trade={isTrade}
                    hasRedDot={buttonNumbers.messages > 0 ? true : false}
                    numberInRedDot={buttonNumbers.messages}
                  />
                  <GridButton
                    navigation={navigation}
                    backgroundIcon="#F96747"
                    source={User}
                    to="FlashBusiness"
                    text="Post a News Flash"
                    trade={isTrade}
                  />
                </>
              )}
              <GridButton navigation={navigation} backgroundIcon="#8ED652" source={Logout} to="Login" text="Logout" trade={isTrade} isLogout={true} />
              {/* <GridButton
              navigation={navigation}
              source={Review}
              to="Reviews"
              text="Reviews"
              trade={isTrade}
            /> */}

              {/* <GridButton
                navigation={navigation}
                source={User}
                to="Profile"
                text="Profile"
                trade={isTrade}
              /> */}
            </View>
          </View>
        </ScrollView>
      </ImageBackground>
    </SafeAreaView>
  );
}
