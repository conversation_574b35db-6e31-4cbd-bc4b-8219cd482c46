import React, { useState } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import Header from '../components/Header';

import { View, Alert, ViewStyle, TextInput, Text, Dimensions, ImageBackground } from 'react-native';
import CustomText from '../components/CustomText';
import CustomTextInput from '../components/CustomTextInput';
import CustomButton from '../components/CustomButton';
import BouncyCheckbox from 'react-native-bouncy-checkbox';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Controller, useForm } from 'react-hook-form';
import { useAuth } from '../../context/AuthContext';
import { postJobs } from '../services/jobsService';
import KeyboardAwareScrollViewCustom from '../components/elements/KeyboardAwareScrollViewCustom';
import moment from 'moment';
import Checkbox from '../components/elements/Checkbox';
import { BackgroundGradient } from '../assets/image';
import { getTradeOptions } from '../constants/tradeOptions';
import PostJobPaymentStep from '../components/PostJobPaymentStep';
import DropdownComponent from '../components/elements/Dropdown';
const DateTimePickerStyle: ViewStyle = {
  alignItems: 'flex-start',
  width: '100%',
};

const tradeOptions = getTradeOptions({ excluded: ['All'] });

export default function PostJob({ navigation, route }) {
  const styles = require('../../style/style');
  const { authState, loadUser } = useAuth();
  const [isLoading, setIsLoading] = React.useState(false);
  // const toggleSwitch = () => setshouldNotify((previousState) => !previousState);
  const [selectedStartDate, setSelectedStartDate] = React.useState(moment().toISOString());
  const [isOther, setIsOther] = React.useState(false);
  const [otherInput, setOtherInput] = React.useState('');
  // const [isPoa, setIsPoa] = React.useState(false); // abandoned
  const [priceForm, setPriceForm] = React.useState('full');
  const [isPaymentStep, setIsPaymentStep] = React.useState(false);

  const [isStandard, setIsStandard] = useState(true);
  const [isPlusPost, setIsPlusPost] = useState(false);

  const { getValues, control, reset, setValue } = useForm({
    defaultValues: {
      trade: [],
      location: '',
      contact_name: '',
      site_name: '',
      short_location: '',
      post_code: '',
      task: '',
      price: '',
      start_date_required: '',
      arrival_date_required: '',
      requirements: [],
    },
  });

  const backgroundStyle: ViewStyle = {
    backgroundColor: Colors.black,
    width: '100%',
    height: '100%',
  };

  const jobForm: ViewStyle = {
    backgroundColor: Colors.white,
    marginTop: 16,
    borderRadius: 25,
    padding: 20,
    flexDirection: 'column',
    gap: 20,
  };

  const row: ViewStyle = {
    flexDirection: 'row',
  };

  const requirementsStyle: ViewStyle = {
    margin: 5,
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    const values = getValues();
    const postData = {
      start_date_required: selectedStartDate,
      location: values.location,
      short_location: values.short_location,
      price: values.price + (priceForm === 'per_hour' ? ' Per hour' : ''),
      requirements: otherInput ? [...values.requirements, otherInput] : values.requirements,
      task: values.task,
      user_post: authState.user?.id,
      site_name: values.site_name,
      contact_name: values.contact_name,
      post_code: values.post_code,
      job_status: 'Open',
      should_notify: false,
      trade: values.trade,
    };
    try {
      const job = await postJobs(postData);
      if (job || !job?.message) {
        await loadUser();
        Alert.alert('Job successfully posted');
        setSelectedStartDate(null);
        reset();
        navigation.reset({
          index: 0,
          routes: [{ name: 'Home' }, { name: 'CurrentPost' }],
        });
        setIsLoading(false);
      } else {
        Alert.alert('Job posting failed');
        setIsLoading(false);
      }
    } catch (error) {
      console.log('error', error);
      Alert.alert('Job posting failed');
      setIsLoading(false);
    }
  };

  const handleChangePayment = () => {
    const values = getValues();
    if (
      !values.location ||
      !values.task ||
      (priceForm !== 'poa' && (!values.price || values.price === '0')) ||
      !values.requirements ||
      !selectedStartDate
    ) {
      Alert.alert('Please fill in all the fields');
      setIsLoading(false);
      return;
    }
    setIsPaymentStep(true);
  };

  return (
    <SafeAreaView style={backgroundStyle}>
      <ImageBackground source={BackgroundGradient} style={{ ...backgroundStyle, height: Dimensions.get('window').height }}>
        <Header
          title="Post a Job"
          navigation={navigation}
          hasBack
          backFunction={async () => {
            const values = getValues();
            if (
              values.location ||
              values.task ||
              values.price ||
              values.requirements.length > 0 ||
              values.trade.length > 0 ||
              values.post_code ||
              values.contact_name ||
              values.site_name
            ) {
              await Alert.alert('You have unsaved changes post job, are you sure you want to go back?', '', [
                { text: 'Cancel', style: 'cancel' },
                {
                  text: 'Yes',
                  onPress: () => {
                    if (navigation.canGoBack()) {
                      navigation.goBack();
                    } else {
                      navigation.navigate('Home');
                    }
                  },
                },
              ]);
            } else {
              if (navigation.canGoBack()) {
                navigation.goBack();
              } else {
                navigation.navigate('Home');
              }
            }
          }}
        />
        <KeyboardAwareScrollViewCustom isEnableScroll>
          <View style={[styles.container, { marginBottom: 100 }]}>
            <View style={jobForm}>
              {isPaymentStep ? (
                <View style={{}}>
                  <PostJobPaymentStep
                    isStandard={isStandard}
                    isPlusPost={isPlusPost}
                    setIsStandard={setIsStandard}
                    setIsPlusPost={setIsPlusPost}
                    isLoading={isLoading}
                    handleSubmit={handleSubmit}
                    setIsPaymentStep={setIsPaymentStep}
                    navigation={navigation}
                    route={route}
                  />
                </View>
              ) : (
                <>
                  <View>
                    <CustomText text="Trade" />
                    {/* <CustomTextInput control={control} name="trade" /> */}
                    <DropdownComponent
                      value={getValues('trade')[0]}
                      data={tradeOptions}
                      onSelectItem={(item) => setValue('trade', [item])}
                      placeholder={'Select Trades'}
                      disabled={isLoading}
                    />
                  </View>
                  <View>
                    <CustomText text="Location (Town/City):" />
                    <CustomTextInput isLoading={isLoading} control={control} name="location" />
                  </View>
                  <View>
                    <CustomText text="Full post code of job location (issued on approval):" />
                    <CustomTextInput isLoading={isLoading} control={control} name="post_code" />
                  </View>

                  <View>
                    <CustomText text="Job contact name (issued on approval):" />
                    <CustomTextInput isLoading={isLoading} control={control} name="contact_name" />
                  </View>

                  <View>
                    <CustomText text="Site name (issued on approval):" />
                    <CustomTextInput isLoading={isLoading} control={control} name="site_name" />
                  </View>

                  <View>
                    <CustomText text="Fix/Task/Job description:" />
                    <CustomTextInput multiline isLoading={isLoading} inputStyle={{ height: 90 }} control={control} name="task" />
                  </View>
                  <View style={{ display: 'flex' }}>
                    <CustomText text="Price:" />
                    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                      <CustomTextInput
                        isLoading={isLoading}
                        isDisabled={priceForm === 'poa'}
                        control={control}
                        name="price"
                        inputStyle={{ paddingLeft: 30, minWidth: 150, width: '60%' }}
                        isNumber
                        prefix={priceForm !== 'poa' && <Text style={{ fontSize: 20, paddingTop: 18, marginRight: 15 }}>£</Text>}
                        suffix={priceForm === 'per_hour' && <Text style={{ fontSize: 16, paddingTop: 18 }}>(Per Hour)</Text>}
                      />
                      <View style={{ flexDirection: 'column', alignItems: 'flex-start', marginLeft: 18 }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                          <Checkbox
                            isChecked={priceForm === 'full'}
                            onPress={() => {
                              if (getValues('price') === 'poa') {
                                setValue('price', '0');
                              }
                              setPriceForm('full');
                            }}
                          />
                          <Text style={{ fontSize: 18 }}>FULL PRICE</Text>
                        </View>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                          <Checkbox
                            isChecked={priceForm === 'per_hour'}
                            onPress={() => {
                              if (getValues('price') === 'poa') {
                                setValue('price', '0');
                              }
                              setPriceForm('per_hour');
                            }}
                          />
                          <Text style={{ fontSize: 18 }}>PER HOUR</Text>
                        </View>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                          <Checkbox
                            isChecked={priceForm === 'poa'}
                            onPress={() => {
                              setValue('price', 'poa');
                              setPriceForm('poa');
                            }}
                          />
                          <Text style={{ fontSize: 18 }}>POA</Text>
                        </View>
                      </View>
                    </View>
                  </View>
                  <View style={{ flexDirection: 'column', alignItems: 'flex-start' }}>
                    <View style={{ alignSelf: 'flex-start', flexDirection: 'row', alignItems: 'center' }}>
                      <CustomText style={{ width: 160 }} text="Start date required:" />

                      <Controller
                        control={control}
                        name={'start_date_required'}
                        defaultValue={selectedStartDate}
                        render={() => (
                          <View style={DateTimePickerStyle}>
                            <DateTimePicker
                              disabled={isLoading}
                              value={selectedStartDate ? new Date(selectedStartDate) : new Date()} // Provide a default value if value is empty
                              mode="date" // You can use "time" or "datetime" for different modes
                              display="default"
                              onChange={(event, date) => {
                                console.log({ onChange: date });
                                setSelectedStartDate(moment(date).toISOString());
                              }}
                              themeVariant="light"
                              accentColor="black"
                            />
                          </View>
                        )}
                      />
                    </View>
                    <View style={{ alignSelf: 'flex-start', marginTop: 10, flexDirection: 'row', alignItems: 'center' }}>
                      <CustomText style={{ width: 160 }} text="Arrival time required:" />
                      <Controller
                        control={control}
                        name={'arrival_date_required'}
                        defaultValue={selectedStartDate}
                        render={() => (
                          <View style={DateTimePickerStyle}>
                            <DateTimePicker
                              disabled={isLoading}
                              value={selectedStartDate ? new Date(selectedStartDate) : new Date()} // Provide a default value if value is empty
                              mode="time" // You can use "time" or "datetime" for different modes
                              display="default"
                              onChange={(event, date) => {
                                setSelectedStartDate(moment(date).toISOString());
                              }}
                              themeVariant="light"
                              accentColor="black"
                            />
                          </View>
                        )}
                      />
                    </View>
                  </View>

                  <View>
                    <CustomText text="Requirements:" style={{ marginBottom: 5 }} />
                    <View style={{ ...row, ...requirementsStyle }}>
                      <Controller
                        control={control}
                        render={({ field: { onChange, value } }) => (
                          <BouncyCheckbox
                            size={20}
                            disabled={isLoading}
                            fillColor={Colors.black}
                            unfillColor={Colors.white}
                            iconStyle={{ borderColor: Colors.gray }}
                            innerIconStyle={{
                              borderWidth: 1,
                              borderColor: Colors.black,
                            }}
                            isChecked={value.includes('Public Liability Insurance (Minimum £5m)')}
                            onPress={(isChecked) => {
                              if (isChecked) {
                                onChange([...value, 'Public Liability Insurance (Minimum £5m)']);
                              } else {
                                onChange(value.filter((item) => item !== 'Public Liability Insurance (Minimum £5m)'));
                              }
                            }}
                          />
                        )}
                        name="requirements"
                      />
                      <CustomText text="Public Liability Insurance (Minimum £5m)" style={{ marginRight: 15 }} />
                    </View>
                    <View style={{ ...row, ...requirementsStyle }}>
                      <Controller
                        control={control}
                        render={({ field: { onChange, value } }) => (
                          <BouncyCheckbox
                            size={20}
                            fillColor={Colors.black}
                            disabled={isLoading}
                            unfillColor={Colors.white}
                            iconStyle={{ borderColor: Colors.gray }}
                            innerIconStyle={{
                              borderWidth: 1,
                              borderColor: Colors.black,
                            }}
                            isChecked={value.includes('Trade Specific CSCS Card or Equivalent')}
                            onPress={(isChecked) => {
                              if (isChecked) {
                                onChange([...value, 'Trade Specific CSCS Card or Equivalent']);
                              } else {
                                onChange(value.filter((item) => item !== 'Trade Specific CSCS Card or Equivalent'));
                              }
                            }}
                          />
                        )}
                        name="requirements"
                      />
                      <CustomText text="Trade Specific CSCS Card or Equivalent" />
                    </View>
                    <View style={{ ...row, ...requirementsStyle }}>
                      <Controller
                        control={control}
                        render={({ field: { onChange, value } }) => (
                          <BouncyCheckbox
                            size={20}
                            disabled={isLoading}
                            fillColor={Colors.black}
                            unfillColor={Colors.white}
                            iconStyle={{ borderColor: Colors.gray }}
                            innerIconStyle={{
                              borderWidth: 1,
                              borderColor: Colors.black,
                            }}
                            isChecked={value.includes('UK Bank Account')}
                            onPress={(isChecked) => {
                              if (isChecked) {
                                onChange([...value, 'UK Bank Account']);
                              } else {
                                onChange(value.filter((item) => item !== 'UK Bank Account'));
                              }
                            }}
                          />
                        )}
                        name="requirements"
                      />
                      <CustomText text="UK Bank Account" />
                    </View>
                    <View style={{ ...row, ...requirementsStyle }}>
                      <Controller
                        control={control}
                        render={({ field: { onChange, value } }) => (
                          <BouncyCheckbox
                            size={20}
                            disabled={isLoading}
                            fillColor={Colors.black}
                            unfillColor={Colors.white}
                            iconStyle={{ borderColor: Colors.gray }}
                            innerIconStyle={{
                              borderWidth: 1,
                              borderColor: Colors.black,
                            }}
                            isChecked={value.includes('Personal Protective Equipment (PPE) (See application for specific requirements)')}
                            onPress={(isChecked) => {
                              if (isChecked) {
                                onChange([...value, 'Personal Protective Equipment (PPE) (See application for specific requirements)']);
                              } else {
                                onChange(
                                  value.filter((item) => item !== 'Personal Protective Equipment (PPE) (See application for specific requirements)')
                                );
                              }
                            }}
                          />
                        )}
                        name="requirements"
                      />
                      <CustomText
                        text="Personal Protective Equipment (PPE) (See application for specific requirements)"
                        style={{ marginRight: 15 }}
                      />
                    </View>
                    <View style={{ ...row, ...requirementsStyle }}>
                      <Controller
                        control={control}
                        render={() => (
                          <BouncyCheckbox
                            size={20}
                            disabled={isLoading}
                            fillColor={Colors.black}
                            unfillColor={Colors.white}
                            iconStyle={{ borderColor: Colors.gray }}
                            innerIconStyle={{
                              borderWidth: 1,
                              borderColor: Colors.black,
                            }}
                            isChecked={isOther}
                            onPress={(isChecked) => {
                              if (isChecked) {
                                setIsOther(true);
                              } else {
                                setOtherInput('');
                                setIsOther(false);
                              }
                            }}
                          />
                        )}
                        name="requirements"
                      />
                      <CustomText text="Other" />
                    </View>
                    {isOther && (
                      <View>
                        <TextInput
                          disableFullscreenUI={isLoading}
                          autoCapitalize="none"
                          autoFocus={false}
                          style={{
                            borderWidth: 1,
                            borderColor: Colors.gray,
                            marginTop: 10,
                            height: 40,
                            paddingHorizontal: 10,
                            fontFamily: 'Montserrat-Regular',
                          }}
                          value={otherInput}
                          blurOnSubmit={false}
                          onChangeText={setOtherInput}
                        />
                      </View>
                    )}
                  </View>
                  <CustomButton
                    navigation={navigation}
                    title={'Proceed to payment information'}
                    onPress={() => handleChangePayment()}
                    color="black"
                  />
                </>
              )}
              {/* <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <CustomText text="Notify all trades: " />
                <Switch
                  disabled={isLoading}
                  trackColor={{ false: '#767577', true: Colors.black }}
                  thumbColor={shouldNotify ? Colors.white : '#f4f3f4'}
                  ios_backgroundColor="#3e3e3e"
                  onValueChange={toggleSwitch}
                  value={shouldNotify}
                />
              </View> */}
            </View>
          </View>
        </KeyboardAwareScrollViewCustom>
      </ImageBackground>
    </SafeAreaView>
  );
}
