import React, { useEffect } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import Header from '../components/Header';

import { View, Alert, ViewStyle, Dimensions, ImageBackground } from 'react-native';
import CustomText from '../components/CustomText';
import CustomTextInput from '../components/CustomTextInput';
import CustomButton from '../components/CustomButton';
import { useForm } from 'react-hook-form';
import { useAuth } from '../../context/AuthContext';
import KeyboardAwareScrollViewCustom from '../components/elements/KeyboardAwareScrollViewCustom';
import FilePicker from '../components/elements/FilePicker';
import { updateFlash } from '../services/flashService';
import { uploadFile } from '../services/fileUploadService';
import DocumentPicker from 'react-native-document-picker';
import { BackgroundGradient } from '../assets/image';

const backgroundStyle: ViewStyle = {
  backgroundColor: Colors.black,
  width: '100%',
  height: '100%',
};

const flashForm: ViewStyle = {
  backgroundColor: Colors.white,
  marginTop: 16,
  borderRadius: 25,
  padding: 20,
  flexDirection: 'column',
  gap: 20,
};

export default function BusinessMyNewsFlashDetail({ route, navigation }) {
  const { newsFlashData } = route.params;
  const styles = require('../../style/style');
  const { authState } = useAuth();
  const userId = authState.user.id;
  const [isLoading, setIsLoading] = React.useState(false);
  const [qualificationFiles, setQualificationFiles] = React.useState([]);

  const { getValues, control, setValue, reset } = useForm({
    defaultValues: {
      title_flash: newsFlashData?.title_flash,
      flash_content: newsFlashData?.flash_content,
      flash_image: newsFlashData?.flash_image,
    },
  });

  const handleSubmit = async () => {
    setIsLoading(true);
    const values = getValues();
    if (!values.title_flash || !values.flash_content) {
      Alert.alert('Please fill in all the fields');
      setIsLoading(false);
      return;
    }
    const documents = await Promise.all(
      qualificationFiles.map(async (file) => {
        if (typeof file === 'string') {
          return file;
        } else {
          const formData = new FormData();
          formData.append('file', file);
          const data = await uploadFile(formData);
          return data.link;
        }
      })
    );
    const flash = await updateFlash({
      title_flash: values.title_flash,
      flash_content: values.flash_content,
      flash_image: documents,
      poster: newsFlashData?.id,
    });

    if (flash) {
      Alert.alert('Update flash successfully', '', [{ text: 'OK' }]);
      setIsLoading(false);
    } else {
      Alert.alert('Update flash failed', '', [{ text: 'OK', onPress: () => setIsLoading(false) }]);
    }
  };

  useEffect(() => {
    setQualificationFiles(newsFlashData?.flash_image);
  }, []);

  return (
    <SafeAreaView style={backgroundStyle}>
      <ImageBackground source={BackgroundGradient} style={{ ...backgroundStyle, height: Dimensions.get('window').height }}>
        <KeyboardAwareScrollViewCustom>
          <Header title="Edit News flash" navigation={navigation} hasBack />
          <View style={styles.container}>
            <View style={flashForm}>
              <View>
                <CustomText text="Title:" />
                <CustomTextInput inputStyle={{ borderRadius: 5 }} isLoading={isLoading} control={control} name="title_flash" />
              </View>
              <View>
                <CustomText text="Content:" />
                <CustomTextInput
                  inputStyle={{ height: 300, borderRadius: 5 }}
                  multiline={true}
                  isLoading={isLoading}
                  control={control}
                  name="flash_content"
                />
              </View>
              <View>
                <CustomText text="Qualifications" />
                <FilePicker
                  files={qualificationFiles}
                  onChange={setQualificationFiles}
                  typePicker={[DocumentPicker.types.images, DocumentPicker.types.pdf]}
                />
              </View>
              <CustomButton
                navigation={navigation}
                title={isLoading ? 'Updating flash..' : 'Update flash'}
                isLoading={isLoading}
                to=""
                onPress={() => handleSubmit()}
                color="black"
              />
            </View>
          </View>
        </KeyboardAwareScrollViewCustom>
      </ImageBackground>
    </SafeAreaView>
  );
}
