import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import Header from '../components/Header';
import { View, ViewStyle, Text, ImageBackground, Dimensions, Linking } from 'react-native';
import moment from 'moment-timezone';
import { BackgroundGradient } from '../assets/image';
import KeyboardAwareScrollViewCustom from '../components/elements/KeyboardAwareScrollViewCustom';
import CustomButton from '../components/CustomButton';

export default function ApprovedJobDetail({ route, navigation }) {
  const { job, company } = route?.params;
  const backgroundStyle: ViewStyle = {
    backgroundColor: Colors.black,
    width: '100%',
    height: '100%',
  };

  const jobForm: ViewStyle = {
    backgroundColor: Colors.white,
    marginTop: 16,
    borderRadius: 25,
    padding: 20,
    flexDirection: 'column',
    gap: 20,
  };

  const textStyle = {
    fontSize: 16,
    marginBottom: 10,
    fontFamily: 'Montserrat-Regular',
  };

  const boldText = {
    fontFamily: 'Montserrat-Bold',
  };

  const openGoogleMaps = () => {
    const address = encodeURIComponent(`${job.location} ${job.post_code}`);
    Linking.openURL(`https://www.google.com/maps/search/?api=1&query=${address}`);
  };

  return (
    <SafeAreaView style={backgroundStyle}>
      <ImageBackground source={BackgroundGradient} style={{ ...backgroundStyle, height: Dimensions.get('window').height }}>
        <Header title="Job Details" navigation={navigation} hasBack />
        <KeyboardAwareScrollViewCustom isEnableScroll>
          <View style={[{ padding: 16, marginBottom: 100 }]}>
            <View style={jobForm}>
              <View>
                <Text style={[textStyle, boldText]}>Trade Required</Text>
                <Text style={textStyle}>{job.trade.join(', ')}</Text>
              </View>

              <View>
                <Text style={[textStyle, boldText]}>Company Details</Text>
                <Text style={textStyle}>Company Name: {company?.user_name || 'N/A'}</Text>
                <Text style={textStyle}>Contact Name: {job.contact_name}</Text>
                <Text style={textStyle}>Site Name: {job.site_name}</Text>
              </View>

              <View>
                <Text style={[textStyle, boldText]}>Location</Text>
                <Text style={textStyle}>{job.location}</Text>
                <CustomButton title="Open in Google Maps" onPress={openGoogleMaps} color="black" style={{ marginTop: 10 }} />
              </View>

              <View>
                <Text style={[textStyle, boldText]}>Job Details</Text>
                <Text style={textStyle}>Description: {job.task}</Text>
                <Text style={textStyle}>Price: £{job.price}</Text>
                <Text style={textStyle}>Start Date: {moment.utc(job.start_date_required).local().format('DD MMM YYYY hh:ssa')}</Text>
              </View>

              <View>
                <Text style={[textStyle, boldText]}>Requirements</Text>
                {job?.requirements?.map((requirement, index) => (
                  <Text key={index} style={textStyle}>
                    • {requirement}
                  </Text>
                ))}
              </View>
            </View>
          </View>
        </KeyboardAwareScrollViewCustom>
      </ImageBackground>
    </SafeAreaView>
  );
}
