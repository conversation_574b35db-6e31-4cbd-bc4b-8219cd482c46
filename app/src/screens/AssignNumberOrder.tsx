import React, { useRef, useState } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import Header from '../components/Header';
import Signature from 'react-native-signature-canvas';

import { View, Alert, TextInput, Text, Dimensions, ImageBackground, TouchableOpacity, ActivityIndicator, StyleSheet } from 'react-native';

import KeyboardAwareScrollViewCustom from '../components/elements/KeyboardAwareScrollViewCustom';
import { BackgroundGradient } from '../assets/image';
import { checkExitOrderNumber } from '../services/usersService';
import axiosInstance from '../utils/axiosManager';
import { useAuth } from '../../context/AuthContext';

export default function AssignNumberOrder({ navigation, route }) {
  const [isLoading, setIsLoading] = useState(false);
  const [signature, setSignature] = useState<string>('');
  const signatureRef = useRef(null);

  const { authState } = useAuth();
  const user = authState.user;

  const [authorizationName, setAuthorizationName] = useState(
    user.order_contact_name || route.params.orderNumberInvoiceDetail?.authorization_name || ''
  );
  const [invoiceEmailAddress, setInvoiceEmailAddress] = useState(
    user.invoicing_contact_email || route.params.orderNumberInvoiceDetail?.invoice_email_address || ''
  );
  const [purchaseOrderNumber, setPurchaseOrderNumber] = useState(user.order_number || route.params.orderNumberInvoiceDetail?.order_number || '');
  const [isSignaturePadActive, setIsSignaturePadActive] = useState(false);

  const handleSave = async () => {
    setIsLoading(true);
    if (authorizationName === '' || invoiceEmailAddress === '' || purchaseOrderNumber === '') {
      Alert.alert('Error', 'Please fill in all fields');
      setIsLoading(false);
      return;
    }

    if (user.order_number === '' || !user.order_number) {
      const orderNumberCheck = await checkExitOrderNumber(purchaseOrderNumber);
      if (orderNumberCheck.success) {
        Alert.alert('Error', 'Order number already exists');
        setIsLoading(false);
        return;
      }
    }

    const responseCreateInvoice = await axiosInstance.post('/api/payment/createInvoice', {
      order_number: purchaseOrderNumber,
      authorization_name: authorizationName,
      invoice_email_address: invoiceEmailAddress,
      amount: route.params.amount,
      currency: route.params.currency,
    });
    if (responseCreateInvoice.data.success) {
      Alert.alert('Success', 'Invoice created successfully, Please pay the invoice', [
        {
          text: 'OK',
          onPress: () =>
            navigation.navigate('PostJob', {
              orderNumberInvoiceDetail: {
                order_number: purchaseOrderNumber,
                authorization_name: authorizationName,
                invoice_email_address: invoiceEmailAddress,
                isHaveAssignOrderNumber: true,
              },
            }),
        },
      ]);
    } else {
      console.log(responseCreateInvoice.data);
      Alert.alert('Error', 'Something went wrong while creating invoice', responseCreateInvoice.data);
    }
    setIsLoading(false);
  };

  const handleSignature = (signature: string) => {
    setSignature(signature);
  };

  return (
    <SafeAreaView style={styles.backgroundStyle}>
      <ImageBackground source={BackgroundGradient} style={{ ...styles.backgroundStyle, height: Dimensions.get('window').height }}>
        <Header
          title="Post a Job"
          navigation={navigation}
          hasBack
          backFunction={async () => {
            if (navigation.canGoBack()) {
              navigation.goBack();
            } else {
              navigation.navigate('Home');
            }
          }}
        />
        <KeyboardAwareScrollViewCustom isEnableScroll={!isSignaturePadActive}>
          <View style={styles.container}>
            <View style={{ width: '100%', paddingVertical: 35, backgroundColor: 'white', paddingHorizontal: 16, borderRadius: 16 }}>
              <Text style={styles.headerTitle}>Assign to order number</Text>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>Purchase order number</Text>
                <TouchableOpacity style={{ ...styles.inputButton, backgroundColor: user.order_number === '' ? Colors.white : '#44444433' }}>
                  <TextInput
                    editable={user.order_number === ''}
                    value={purchaseOrderNumber}
                    onChangeText={setPurchaseOrderNumber}
                    placeholder="Enter order number"
                    placeholderTextColor={'#666666'}
                    style={styles.textInput}
                  />
                </TouchableOpacity>

                <Text style={styles.label}>Authorization name</Text>
                <TouchableOpacity style={styles.inputButton}>
                  <TextInput
                    value={authorizationName}
                    onChangeText={setAuthorizationName}
                    placeholder="Enter name"
                    placeholderTextColor={'#666666'}
                    style={styles.textInput}
                  />
                </TouchableOpacity>

                <Text style={styles.label}>Invoice email address</Text>
                <TouchableOpacity style={styles.inputButton}>
                  <TextInput
                    value={invoiceEmailAddress}
                    onChangeText={setInvoiceEmailAddress}
                    placeholder="Enter email address"
                    placeholderTextColor={'#666666'}
                    style={styles.textInput}
                  />
                </TouchableOpacity>
                <Text style={styles.label}>Signature</Text>
                <View>
                  <View style={styles.signatureContainer}>
                    <Signature
                      onOK={handleSignature}
                      descriptionText="Sign above"
                      ref={(ref) => (signatureRef.current = ref)}
                      onBegin={() => setIsSignaturePadActive(true)}
                      onEnd={() => setIsSignaturePadActive(false)}
                    />
                  </View>
                  <TouchableOpacity style={styles.resetButton} onPress={() => signatureRef.current?.clearSignature()}>
                    <Text style={styles.resetButtonText}>Reset Signature</Text>
                  </TouchableOpacity>
                </View>

                <TouchableOpacity style={styles.saveButton} onPress={handleSave} disabled={isLoading}>
                  {isLoading ? <ActivityIndicator color={Colors.white} /> : <Text style={styles.saveButtonText}>Save & Confirm</Text>}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </KeyboardAwareScrollViewCustom>
      </ImageBackground>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginBottom: 100,
    paddingHorizontal: 16,
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginTop: 32,
  },
  backgroundStyle: {
    backgroundColor: Colors.black,
    width: '100%',
    height: '100%',
  },
  text: {
    fontSize: 16,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  button: {
    marginTop: 15,
    paddingVertical: 7,
    paddingHorizontal: 15,
    borderWidth: 1,
    borderRadius: 50,
    minHeight: 40,
    width: '100%',
    maxWidth: 350,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    columnGap: 10,
  },
  buttonText: {
    textTransform: 'uppercase',
  },
  buttonConfirm: {
    marginTop: 25,
    textTransform: 'uppercase',
    textAlign: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    color: Colors.white,
    fontWeight: 'bold',
    fontSize: 16,
    paddingVertical: 10,
    backgroundColor: Colors.black,
    borderRadius: 50,
    width: '100%',
    maxWidth: 130,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  input: {
    width: '100%',
    maxWidth: 80,
    height: 35,
    borderWidth: 1,
    borderColor: Colors.gray,
    padding: 5,
    marginTop: 10,
    marginBottom: 15,
    borderRadius: 10,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 30,
    textAlign: 'center',
  },
  inputContainer: {
    width: '100%',
    gap: 15,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 5,
  },
  inputButton: {
    borderWidth: 1,
    borderColor: Colors.gray,
    borderRadius: 8,
    padding: 10,
    width: '100%',
  },
  textInput: {
    fontSize: 16,
  },
  signatureButton: {
    borderWidth: 1,
    borderColor: Colors.gray,
    borderRadius: 8,
    padding: 10,
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveButton: {
    backgroundColor: Colors.black,
    borderRadius: 25,
    padding: 15,
    marginTop: 30,
    alignItems: 'center',
  },
  saveButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  signaturePadContainer: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backgroundColor: 'white',
    zIndex: 999,
  },
  signatureContainer: {
    height: 250,
    paddingBottom: 15,
    width: '100%',
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#000',
  },
  resetButton: {
    marginTop: 8,
    alignSelf: 'flex-end',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    backgroundColor: '#f0f0f0',
  },
  resetButtonText: {
    color: Colors.black,
    fontSize: 14,
  },
  cancelButton: {
    marginTop: 10,
    alignSelf: 'center',
  },
  cancelButtonText: {
    color: Colors.black,
    fontSize: 16,
  },
});
