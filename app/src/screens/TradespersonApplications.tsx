import React, { useEffect, useMemo } from 'react';
import { ScrollView } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import { View, Dimensions, ActivityIndicator, ImageBackground, TouchableOpacity, Text, Alert } from 'react-native';
import Header from '../components/Header';
import MyApplicationCard from '../components/elements/MyApplicationCard';
import { BackgroundGradient } from '../assets/image';
import { hideExpiredJobsApplication, useListTradespersonApplications } from '../services/jobsService';
import CustomText from '../components/CustomText';
import moment from 'moment-timezone';
import CustomButton from '../components/CustomButton';
import Popover from 'react-native-popover-view/dist/Popover';
import SelectDropdown from 'react-native-select-dropdown';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useAuth } from '../../context/AuthContext';

const dropdownButtonStyle = {
  width: 150,
  height: 30,
  backgroundColor: '#E9ECEF',
  borderRadius: 40,
  flexDirection: 'row',
  justifyContent: 'center',
  alignItems: 'center',
  paddingHorizontal: 12,
};
const dropdownButtonTxtStyle = {
  flex: 1,
  fontSize: 18,
  fontWeight: '500',
  color: '#151E26',
};
const dropdownButtonArrowStyle = {
  fontSize: 28,
};
const dropdownItemStyle = {
  width: '100%',
  flexDirection: 'row',
  paddingHorizontal: 12,
  justifyContent: 'center',
  alignItems: 'center',
  paddingVertical: 8,
};
const dropdownItemTxtStyle = {
  flex: 1,
  fontSize: 18,
  fontWeight: '500',
  color: '#151E26',
};
const dropdownMenuStyle = {
  backgroundColor: '#E9ECEF',
  borderRadius: 8,
};

export default function TradespersonApplications({ route, navigation }) {
  const { list: rawlist, isFetching, reFetch } = useListTradespersonApplications();

  const { authState } = useAuth();

  const [isLoading, setIsLoading] = React.useState(false);
  const [isFilterVisible, setFilterVisible] = React.useState(false);

  const [sortBy, setSortBy] = React.useState('date');

  const list = useMemo(() => {
    if (sortBy === 'date') {
      return rawlist.sort((a, b) => moment(b.date).diff(moment(a.date)));
    }
    if (sortBy === 'company') {
      return rawlist.sort((a, b) => a.business[0].user_name.localeCompare(b.business[0].user_name));
    }
  }, [rawlist, sortBy]);

  useEffect(() => {
    // navigation on focus screen load user
    const unsubscribe = navigation.addListener('focus', () => {
      reFetch();
    });
    return () => {
      unsubscribe();
    };
  }, []);

  const styles = require('../../style/style');

  const backgroundStyle: any = {
    backgroundColor: Colors.black,
    width: '100%',
    height: '100%',
  };

  const handleHideExpiredJobs = async () => {
    const jobIds = list.filter((item) => item.job_status[0] === 'Rejected' || item.job_status[0] === 'Completed').map((item) => item.id);
    console.log('jobIds', jobIds);
    setIsLoading(true);
    try {
      const res = await hideExpiredJobsApplication(jobIds);
      console.log('res', res);
      if (res.success) {
        reFetch();
        Alert.alert('Success', 'Hide expired jobs successfully');
      } else {
        Alert.alert('Error', res.message);
      }
    } catch (error) {
      Alert.alert('Error', 'Fail to hide expired jobs');
    }
    setIsLoading(false);
  };

  const isHaveRejectedOrCompletedJobs = list.filter((item) => item.job_status[0] === 'Rejected' || item.job_status[0] === 'Completed').length > 0;
  return (
    <SafeAreaView style={backgroundStyle}>
      <ImageBackground source={BackgroundGradient} style={{ ...backgroundStyle, height: Dimensions.get('window').height }}>
        <Header title="My Applications" navigation={navigation} hasBack />

        {/* Filter section */}
        <View style={{ flexDirection: 'row', marginHorizontal: 15, marginTop: 15, justifyContent: 'space-evenly' }}>
          <View>
            <CustomButton
              color="black"
              style={{
                width: Dimensions.get('window').width / 2 - 30,
                backgroundColor: isHaveRejectedOrCompletedJobs ? '#1b53ac' : '#808080',
                borderRadius: 25,
                padding: 15,
                height: 55,
              }}
              disabled={!isHaveRejectedOrCompletedJobs}
              isLoading={isLoading}
              onPress={handleHideExpiredJobs}
              title="Clear rejected/completed jobs"
            />
          </View>

          <View style={{ backgroundColor: Colors.white, borderRadius: 25 }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
              <Popover
                isVisible={isFilterVisible}
                arrowSize={{ width: 0, height: 0 }}
                onRequestClose={() => setFilterVisible(false)}
                popoverStyle={{ backgroundColor: Colors.white, borderRadius: 25, padding: 15, width: Dimensions.get('window').width - 30 }}
                from={
                  <TouchableOpacity
                    onPress={() => setFilterVisible(true)}
                    style={{
                      height: 55,
                      flexDirection: 'row',
                      width: Dimensions.get('window').width / 2 - 30,
                      alignSelf: 'center',
                      justifyContent: 'center',
                      alignItems: 'center',
                      padding: 0,
                    }}
                  >
                    <Text style={{ fontSize: 16 }}>Sorting</Text>
                  </TouchableOpacity>
                }
              >
                <SortingComponent setSortBy={setSortBy} sortBy={sortBy} />
              </Popover>
            </View>
          </View>
        </View>

        <ScrollView style={[styles.container, { marginBottom: 100 }]}>
          <View>
            {isFetching ? (
              <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', marginTop: 20 }}>
                <ActivityIndicator size="large" color={Colors.white} />
              </View>
            ) : (
              <View style={{ paddingTop: 10, marginTop: 15, gap: 10 }}>
                {!list || list.length === 0 ? (
                  <CustomText text={'You have not submitted any application'} color="white" />
                ) : (
                  <>
                    {list.map((item, index) => {
                      const jobStatus = item.job_status[0];
                      const companyName = item.business[0]?.user_name;
                      const job = item.job[0];
                      let itemUserId = item.business[0]?.id;
                      return (
                        <MyApplicationCard
                          key={'job-card-' + index}
                          company={item.business[0]}
                          name={`${jobStatus} - ${companyName}`}
                          title={job?.post_title}
                          job={job}
                          status={jobStatus}
                          jobPrice={job?.price}
                          jobDate={job?.start_date_required}
                          jobLocation={job?.location}
                          detail={`£${job?.price}/h - ${moment.utc(job?.start_date_required).local().format('DD MMM YYYY hh:ssa')} - ${
                            job?.location
                          }`}
                          businessId={item.business[0]?.id}
                          onPress={() => {}}
                          navigation={navigation}
                          isViewApplication
                          applicationId={item.id}
                          userId={itemUserId}
                          userName={companyName}
                          isOwner={authState.user.id === itemUserId}
                          isLeftReviewFromTrade={item.is_left_review_from_trade[0] === 'yes'}
                        />
                      );
                    })}
                  </>
                )}
              </View>
            )}
          </View>
        </ScrollView>
      </ImageBackground>
    </SafeAreaView>
  );
}

const SortingComponent = ({ setSortBy, sortBy }) => {
  const sortingOption = [
    { title: 'Date', value: 'date' },
    { title: 'Company', value: 'company' },
  ];
  const getOption = (value: string) => {
    return sortingOption.find((item) => item.value === value);
  };

  return (
    <View style={{ flexDirection: 'column', justifyContent: 'space-between', alignItems: 'center', backgroundColor: Colors.white }}>
      <View style={{ flexDirection: 'column', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 0 }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', marginRight: 10 }}>
          <CustomText style={{ width: 120 }} text="Sort by: " color="black" />
          <SelectDropdown
            data={sortingOption}
            defaultValue={getOption(sortBy)}
            onSelect={(selectedItem) => {
              setSortBy(selectedItem.value);
            }}
            renderButton={(selectedItem, isOpened) => {
              return (
                <View style={dropdownButtonStyle}>
                  <Text style={dropdownButtonTxtStyle}>{selectedItem && selectedItem.title}</Text>
                  <Icon name={isOpened ? 'chevron-up' : 'chevron-down'} style={dropdownButtonArrowStyle} />
                </View>
              );
            }}
            renderItem={(item, index, isSelected) => {
              return (
                <View style={{ ...dropdownItemStyle, ...(isSelected && { backgroundColor: '#D2D9DF' }) }}>
                  <Text style={dropdownItemTxtStyle}>{item.title}</Text>
                </View>
              );
            }}
            showsVerticalScrollIndicator={false}
            dropdownStyle={dropdownMenuStyle}
          />
        </View>
      </View>
    </View>
  );
};
