import React, { FC, useState } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import CustomButton from './CustomButton';
import DateTimePicker, { AndroidNativeProps, IOSNativeProps, WindowsNativeProps } from '@react-native-community/datetimepicker';

interface Props extends React.ComponentProps<typeof DateTimePicker> {}

export const AppDateTimePicker = (props: Props) => {
  const [date, setDate] = useState(new Date(1598051730000));
  const [mode, setMode] = useState( 'date');
  const [show, setShow] = useState(false);

  const onChange = (event, selectedDate) => {
    const currentDate = selectedDate;
    setShow(false);
    setDate(currentDate);
  };

  const showMode = (currentMode) => {
    setShow(true);
    setMode(currentMode);
  };

  const showDatepicker = () => {
    showMode('date');
  };

  const showTimepicker = () => {
    showMode('time');
  };

  return (
    <SafeAreaView>
      <CustomButton onPress={showDatepicker} title="Show date picker!" color="black" />
      <CustomButton onPress={showTimepicker} title="Show time picker!" color="black" />
      {show && <DateTimePicker value={date} mode={mode} is24Hour={true} onChange={onChange} />}
    </SafeAreaView>
  );
};
