import React from 'react';

import { View, StyleSheet, Image, TouchableOpacity, ViewStyle } from 'react-native';
import { Logo } from '../assets/image';
import CustomText from './CustomText';
import { useAuth } from '../../context/AuthContext';
import AntDesign from 'react-native-vector-icons/AntDesign';

type HeaderProps = {
  navigation: any;
  hasBack?: boolean;
  backFunction?: () => void;
  imageContainerStyle?: ViewStyle;
  isHome?: boolean;
  title?: string;
};
export default function Header({
  navigation,
  hasBack = true,
  backFunction,
  isHome = false,
  title = '',
  imageContainerStyle = {
    width: 100,
    height: 80,
  },
}: HeaderProps) {
  const { authState } = useAuth();
  const isTrade = authState.isTrade;
  const imageContainer = imageContainerStyle;

  return (
    <View style={styles.container}>
      {isHome ? (
        <>
          <View>
            {authState.token ? (
              <View style={{ marginTop: 10 }}>
                {isTrade ? (
                  <View>
                    <CustomText text={authState.user?.user_name} color="white" style={styles.textHeaderStyle} />
                    <CustomText text="Tradesperson" color="white" />
                  </View>
                ) : (
                  <View>
                    <CustomText text={authState.user?.user_name} color="white" style={styles.textHeaderStyle} />
                    <CustomText text={'Business'} color="white" />
                  </View>
                )}
              </View>
            ) : (
              <View></View>
            )}
          </View>
          <TouchableOpacity
            onPress={() => {
              authState.token ? navigation.navigate('Home') : navigation.navigate('Login');
            }}
          >
            <View style={imageContainer}>
              <Image source={Logo} style={styles.imageStyle}></Image>
            </View>
          </TouchableOpacity>
        </>
      ) : (
        <View style={{ flex: 1, position: 'relative' }}>
          <View style={{ alignSelf: 'flex-start' }}>
            {hasBack && (
              <TouchableOpacity
                onPress={() => {
                  if (backFunction) {
                    backFunction();
                  } else if (navigation.canGoBack()) {
                    navigation.goBack();
                  } else navigation.navigate('Home');
                }}
                style={{
                  position: 'absolute',
                  width: 40,
                  padding: 5,
                  top: -3,
                  flexDirection: 'row',
                  alignItems: 'center',
                }}
              >
                <AntDesign name="arrowleft" size={25} color="white" />
              </TouchableOpacity>
            )}
          </View>
          <View style={{ alignSelf: 'center' }}>
            <CustomText style={{ fontSize: 22 }} text={title} color="white" />
          </View>
          <TouchableOpacity
            style={{
              position: 'absolute',
              width: 40,
              height: 40,
              right: 0,
              padding: 5,
              flexDirection: 'row',
              alignItems: 'center',
              bottom: -5,
            }}
            onPress={() => {
              authState.token ? navigation.navigate('Home') : navigation.navigate('Login');
            }}
          >
            <View style={imageContainer}>
              <Image source={Logo} style={{ ...styles.imageStyle, width: 40, height: 40 }}></Image>
            </View>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginTop: 32,
    paddingHorizontal: 16,
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  imageStyle: {
    flex: 1,
    width: null,
    height: null,
    resizeMode: 'contain',
  },
  textHeaderStyle: {
    fontSize: 20,
  },
});
