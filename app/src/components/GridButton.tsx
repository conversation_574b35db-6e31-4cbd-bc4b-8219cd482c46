import React from 'react';
import { Pressable, Dimensions, Image, View, ViewStyle } from 'react-native';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import CustomText from './CustomText';
import { useAuth } from '../../context/AuthContext';

interface GridButtonProps {
  navigation: any;
  source: any;
  to: string;
  text: string;
  trade: boolean;
  isLogout?: boolean;
  backgroundIcon?: string;
  hasRedDot?: boolean;
  numberInRedDot?: number;
}

export default function GridButton({ navigation, source, to, text, trade, isLogout, backgroundIcon, hasRedDot, numberInRedDot }: GridButtonProps) {
  const { onLogout } = useAuth();
  const gridButton = {
    width: (Dimensions.get('window').width - 70) / 3,
    height: (Dimensions.get('window').width - 70) / 3,
    marginBottom: 10,
    flexDirection: 'column',
    alignItems: 'center',
  };

  const IconButton = {
    width: (Dimensions.get('window').width - 120) / 3,
    height: (Dimensions.get('window').width - 120) / 3,
    marginBottom: 5,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: backgroundIcon || Colors.white,
  };

  const imageStyle = {
    width: 40,
    height: 40,
    resizeMode: 'contain',
  };

  const redDot: ViewStyle = {
    position: 'absolute',
    width: 20,
    height: 20,
    borderRadius: 20,
    top: 4,
    right: 13,
    backgroundColor: '#d83a52',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  };

  return (
    <Pressable
      style={gridButton}
      onPress={() => {
        if (isLogout) {
          onLogout().then(() => {
            console.log('Logout completed, navigating to:', to);
            navigation.navigate(to, { isTrade: trade });
          });
        } else {
          navigation.navigate(to, { isTrade: trade });
        }
      }}
    >
      {hasRedDot && (
        <View style={redDot}>{numberInRedDot !== undefined && <CustomText text={numberInRedDot} style={{ color: Colors.white }} />}</View>
      )}
      <View style={IconButton}>
        <Image source={source} style={imageStyle} />
      </View>
      <CustomText text={text} style={{ color: Colors.white }} position="center" />
    </Pressable>
  );
}
