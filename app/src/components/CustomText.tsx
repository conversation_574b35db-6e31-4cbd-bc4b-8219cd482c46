import React from 'react';

import { Text } from 'react-native';
import { Colors } from 'react-native/Libraries/NewAppScreen';

export default function CustomText({
  text,
  isBold = false,
  position = 'left',
  color = 'black',
  isUnderline = false,
  onPress = undefined,
  style = undefined,
  numberOfLines = 0,
}) {
  const black = {
    color: Colors.black,
  };

  const white = {
    color: Colors.white,
  };

  const bold = {
    fontFamily: 'Montserrat-Bold',
  };

  const center = {
    textAlign: 'center',
  };

  const right = {
    textAlign: 'right',
  };

  const underline = {
    textDecorationLine: 'underline',
  };

  const fontFamily = {
    fontFamily: 'Montserrat-Regular',
  };

  return (
    <Text
      style={[
        fontFamily,
        isBold && bold,
        color === 'black' && black,
        color === 'white' && white,
        position === 'center' && center,
        position === 'right' && right,
        isUnderline && underline,
        style,
      ]}
      numberOfLines={numberOfLines}
      onPress={onPress}
    >
      {text}
    </Text>
  );
}
