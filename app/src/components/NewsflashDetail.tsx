import React, { useEffect } from 'react';
import { ScrollView } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import { StyleSheet, View, Text, Image, Dimensions, Share, TouchableOpacity, ActivityIndicator, ImageBackground } from 'react-native';
import Header from './Header';
import CustomText from './CustomText';
import { BackgroundGradient, Forward } from '../assets/image';
import { getFlash, getFlashById, setReadFlash } from '../services/flashService';
import { utils } from '../utils';
import WebView from 'react-native-webview';
import CustomButton from './CustomButton';

const backgroundStyle = {
  backgroundColor: Colors.black,
  width: '100%',
  height: '100%',
};

const newsForm = {
  backgroundColor: Colors.white,
  marginTop: 0,
  borderRadius: 25,
  padding: 20,
  flexDirection: 'column',
  gap: 20,
  height: Dimensions.get('window').height - 200,
};

const formRow = {
  flex: 1,
  marginBottom: 15,
  paddingBottom: 15,
};

const pdfStyle = {
  marginTop: 30,
  flex: 1,
  height: ((Dimensions.get('window').width - 70) * 4) / 3,
  width: Dimensions.get('window').width - 70,
};

const imageStyle = {
  width: Dimensions.get('window').width - 70,
  height: undefined,
  alignSelf: 'center',
  aspectRatio: 1,
};
const styles = require('../../style/style');

export default function NewsflashDetail({ route, navigation }) {
  const { newsFlashId, newsFlashData, isEdit, isNoti, isRead } = route.params;
  const [newsFlash, setNewsFlash] = React.useState();
  const [isLoading, setIsLoading] = React.useState(true);
  const [isLoadingImage, setIsLoadingImage] = React.useState(true);
  const getNewsFlashDetail = async (id) => {
    setIsLoading(true);
    try {
      const response = await getFlashById(id);
      if (response) setNewsFlash(response);
      setIsLoading(false);
    } catch (error) {
      utils.handleErrorNotify(error);
      setIsLoading(false);
    }
  };

  function checkFileType(url: string) {
    const extension = url.split('.').pop()?.toLowerCase();

    switch (extension) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return 'image';
      case 'pdf':
        return 'pdf';
      default:
        return 'unknown';
    }
  }
  useEffect(() => {
    setIsLoading(true);
    if (newsFlashData) {
      setNewsFlash(newsFlashData);
      if (!isRead) {
        setReadFlash(newsFlashData.id);
      }
      setIsLoading(false);
      return;
    } else if (newsFlashId) {
      getNewsFlashDetail(newsFlashId);
    }
  }, []);

  return (
    <SafeAreaView style={backgroundStyle}>
      <ImageBackground source={BackgroundGradient} style={{ ...backgroundStyle, height: Dimensions.get('window').height }}>
        <Header title="Newsflash" navigation={navigation} hasBack={isNoti ? false : true} />
        <View style={styles.container}>
          {newsFlash && (
            <View style={newsForm}>
              <View style={formRow}>
                {isEdit && (
                  <CustomButton
                    onPress={() => navigation.navigate('BusinessMyNewsFlashDetail', { newsFlashData })}
                    title="Edit"
                    color="black"
                    style={{ marginBottom: 15, borderRadius: 10 }}
                  />
                )}
                {isLoading || !newsFlash ? (
                  <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                    <ActivityIndicator size="large" color={Colors.black} />
                  </View>
                ) : (
                  <>
                    <ScrollView
                      style={{ flex: 1 }}
                      onScroll={({ nativeEvent }) => {
                        if (nativeEvent.contentOffset.y <= -100) {
                          getNewsFlashDetail(newsFlashId);
                        }
                      }}
                      scrollEventThrottle={50}
                    >
                      <CustomText style={{ ...styles.titleMedium, marginBottom: 20 }} text={newsFlash?.title_flash} />
                      <CustomText text={newsFlash?.flash_content} />
                      {newsFlash?.flash_image &&
                        newsFlash?.flash_image?.length > 0 &&
                        newsFlash?.flash_image.map((item, index) => {
                          return (
                            <React.Fragment key={index}>
                              {item && (
                                <>
                                  {checkFileType(item) === 'pdf' ? (
                                    <WebView style={pdfStyle} originWhitelist={['*']} source={{ uri: item }} />
                                  ) : (
                                    <View>
                                      {isLoadingImage && <ActivityIndicator style={{ marginTop: 30 }} size="small" color={Colors.black} />}
                                      <Image
                                        style={imageStyle}
                                        resizeMode="contain"
                                        source={{
                                          uri: item,
                                        }}
                                        onLoadEnd={() => setIsLoadingImage(false)}
                                      />
                                    </View>
                                  )}
                                </>
                              )}
                            </React.Fragment>
                          );
                        })}
                    </ScrollView>
                  </>
                )}
              </View>
            </View>
          )}
        </View>
      </ImageBackground>
    </SafeAreaView>
  );
}
