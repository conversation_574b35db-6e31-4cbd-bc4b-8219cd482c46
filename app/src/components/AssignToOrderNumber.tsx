import { Dimensions, StyleSheet, Text, TouchableOpacity, View, TextInput, Alert, ActivityIndicator } from 'react-native';
import React, { useState, useRef } from 'react';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import { checkExitOrderNumber } from '../services/usersService';
import Signature from 'react-native-signature-canvas';
import axiosInstance from '../utils/axiosManager';

type AssignOrderNumberProps = {
  purchaseOrderNumber: string;
  authorizationName: string;
  invoiceEmailAddress: string;
  defaultPurchaseOrderNumber: string;
  setInvoiceEmailAddress: (value: string) => void;
  setAuthorizationName: (value: string) => void;
  setPurchaseOrderNumber: (value: string) => void;
  setIsAssignOrderNumber: (value: boolean) => void;
  handleCancel: () => void;
  amount: number;
  currency: string;
};
export default function AssignOrderNumber({
  purchaseOrderNumber,
  authorizationName,
  invoiceEmailAddress,
  defaultPurchaseOrderNumber,
  setAuthorizationName,
  setPurchaseOrderNumber,
  setInvoiceEmailAddress,
  setIsAssignOrderNumber,
  handleCancel,
  amount,
  currency,
}: AssignOrderNumberProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [signature, setSignature] = useState<string>('');
  const signatureRef = useRef(null);

  const handleSave = async () => {
    setIsLoading(true);
    if (authorizationName === '' || invoiceEmailAddress === '' || purchaseOrderNumber === '') {
      Alert.alert('Error', 'Please fill in all fields');
      setIsLoading(false);
      return;
    }

    if (defaultPurchaseOrderNumber === '') {
      const orderNumberCheck = await checkExitOrderNumber(purchaseOrderNumber);
      if (orderNumberCheck.success) {
        Alert.alert('Error', 'Order number already exists');
        setIsLoading(false);
        return;
      }
    }
    const responseCreateInvoice = await axiosInstance.post('/api/payment/createInvoice', {
      amount,
      currency,
      order_number: purchaseOrderNumber,
      authorization_name: authorizationName,
      invoice_email_address: invoiceEmailAddress,
      // signature_base64: signature,
    });
    if (responseCreateInvoice.data.success) {
      Alert.alert('Success', 'Invoice created successfully, Please pay the invoice');
    } else {
      Alert.alert('Error', responseCreateInvoice.data.error);
    }
    setIsLoading(false);
    setIsAssignOrderNumber(false);
  };

  const handleSignature = (signature: string) => {
    setSignature(signature);
  };

  return (
    <View style={styles.container}>
      <View style={{ width: '100%', paddingTop: 35 }}>
        <Text style={styles.headerTitle}>Assign to order number</Text>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Purchase order number</Text>
          <TouchableOpacity style={{ ...styles.inputButton, backgroundColor: defaultPurchaseOrderNumber === '' ? Colors.white : '#44444433' }}>
            <TextInput
              editable={defaultPurchaseOrderNumber === ''}
              value={purchaseOrderNumber.charAt(0).toLowerCase() + purchaseOrderNumber.slice(1)}
              onChangeText={setPurchaseOrderNumber}
              placeholder="enter order number"
              style={styles.textInput}
            />
          </TouchableOpacity>

          <Text style={styles.label}>Authorization name</Text>
          <TouchableOpacity style={styles.inputButton}>
            <TextInput value={authorizationName} onChangeText={setAuthorizationName} placeholder="Enter name" style={styles.textInput} />
          </TouchableOpacity>

          <Text style={styles.label}>Invoice email address</Text>
          <TouchableOpacity style={styles.inputButton}>
            <TextInput value={invoiceEmailAddress} onChangeText={setInvoiceEmailAddress} placeholder="Enter email address" style={styles.textInput} />
          </TouchableOpacity>
          <Text style={styles.label}>Signature</Text>
          <View>
            <View style={styles.signatureContainer}>
              <Signature
                onOK={handleSignature}
                onEnd={() => {
                  signatureRef.current?.readSignature();
                }}
                nestedScrollEnabled
                descriptionText="Sign above"
                webStyle={`.m-signature-pad--footer {display: none} .m-signature-pad {border: none; box-shadow: none} .m-signature-pad--body {border: 1px solid #cccccc; border-radius: 8px}`}
                bgHeight={150}
                ref={(ref) => (signatureRef.current = ref)}
              />
            </View>
            <TouchableOpacity style={styles.resetButton} onPress={() => signatureRef.current?.clearSignature()}>
              <Text style={styles.resetButtonText}>Reset Signature</Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity style={styles.saveButton} onPress={handleSave} disabled={isLoading}>
            {isLoading ? <ActivityIndicator color={Colors.white} /> : <Text style={styles.saveButtonText}>Save & Confirm</Text>}
          </TouchableOpacity>
          <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  backgroundStyle: {
    backgroundColor: Colors.black,
    width: '100%',
    height: '100%',
  },
  text: {
    fontSize: 16,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  button: {
    marginTop: 15,
    paddingVertical: 7,
    paddingHorizontal: 15,
    borderWidth: 1,
    borderRadius: 50,
    minHeight: 40,
    width: '100%',
    maxWidth: 350,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    columnGap: 10,
  },
  buttonText: {
    textTransform: 'uppercase',
  },
  buttonConfirm: {
    marginTop: 25,
    textTransform: 'uppercase',
    textAlign: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    color: Colors.white,
    fontWeight: 'bold',
    fontSize: 16,
    paddingVertical: 10,
    backgroundColor: Colors.black,
    borderRadius: 50,
    width: '100%',
    maxWidth: 130,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  checkboxContainer: {
    width: Dimensions.get('window').width - 100,
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 10,
  },
  input: {
    width: '100%',
    maxWidth: 80,
    height: 35,
    borderWidth: 1,
    borderColor: Colors.gray,
    padding: 5,
    marginTop: 10,
    marginBottom: 15,
    borderRadius: 10,
  },
  balance: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 15,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 30,
    textAlign: 'center',
  },
  inputContainer: {
    width: '100%',
    gap: 15,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 5,
  },
  inputButton: {
    borderWidth: 1,
    borderColor: Colors.gray,
    borderRadius: 8,
    padding: 10,
    width: '100%',
  },
  textInput: {
    fontSize: 16,
  },
  signatureButton: {
    borderWidth: 1,
    borderColor: Colors.gray,
    borderRadius: 8,
    padding: 10,
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveButton: {
    backgroundColor: Colors.black,
    borderRadius: 25,
    padding: 15,
    marginTop: 30,
    alignItems: 'center',
  },
  saveButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  signaturePadContainer: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backgroundColor: 'white',
    zIndex: 999,
  },
  signatureContainer: {
    height: 150,
    width: '100%',
    borderRadius: 8,
    overflow: 'hidden',
  },
  resetButton: {
    marginTop: 8,
    alignSelf: 'flex-end',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    backgroundColor: '#f0f0f0',
  },
  resetButtonText: {
    color: Colors.black,
    fontSize: 14,
  },
  cancelButton: {
    marginTop: 10,
    alignSelf: 'center',
  },
  cancelButtonText: {
    color: Colors.black,
    fontSize: 16,
  },
});
