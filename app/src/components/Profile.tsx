import React, { useEffect, useState } from 'react';
import { ScrollView } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import Header from './Header';
import { View, Text, Image, Dimensions, Alert, ActivityIndicator, ImageBackground } from 'react-native';
import CustomText from './CustomText';
import { useAuth } from '../../context/AuthContext';
const styles = require('../../style/style');
import StarRating from 'react-native-star-rating';
import CustomButton from './CustomButton';
import WebView from 'react-native-webview';
import axios from 'axios';
import { getUserById } from '../services/usersService';
import { businessApproveJob } from '../services/jobsService';
import Modal from 'react-native-modal';
import { getConversationDetail, sendMessage } from '../services/messagesService';
import { BackgroundGradient, ProfileIcon, TradesProfileIcon } from '../assets/image';
import { Linking } from 'react-native';

const backgroundStyle = {
  backgroundColor: Colors.black,
  width: '100%',
  height: '100%',
};

const profileForm = {
  backgroundColor: Colors.white,
  borderRadius: 25,
  flexDirection: 'column',
  height: Dimensions.get('window').height - 205,
};

const formTitle = {
  fontSize: 24,
  flex: 1,
  color: Colors.black,
  fontFamily: 'Montserrat-Regular',
};

const title = {
  fontWeight: 'bold',
  fontSize: 18,
};
const text = {
  marginTop: 10,
  fontSize: 16,
};

const titleRow = {
  flexDirection: 'row',
  alignItems: 'center',
  borderBottomColor: Colors.gray,
  paddingBottom: 10,
  borderBottomWidth: 1,
};

const webViewStyle = {
  flex: 1,
  width: Dimensions.get('window').width - 70,
  height: 250,
  overflow: 'hidden',
  marginTop: 10,
  marginBottom: 20,
};

const uploadButton = {
  padding: 5,
  flexDirection: 'row',
  justifyContent: 'center',
  alignItems: 'center',
  columnGap: 5,
  backgroundColor: 'white',
  borderRadius: 100,
  height: 100,
  width: 100,
  borderWidth: 1,
  borderStyle: 'dashed',
  borderColor: 'rgba(0,0,0,1)',
};

const uploadedFileContainer = {
  flexDirection: 'row',
  alignItems: 'flex-start',
  justifyContent: 'flex-start',
  backgroundColor: 'rgba(0,0,0,0.1)',
  borderRadius: 100,
  margin: 5,
  height: 100,
  width: 100,
  borderColor: 'rgba(0,0,0,0.1)',
};
const thumb = {
  aspectRatio: 1,
  borderRadius: 100,
  height: 100,
  width: 100,
  overflow: 'hidden',
  backgroundColor: 'transparent',
  justifyContent: 'center',
  alignItems: 'center',
};
const editButtonStyle = {
  alignSelf: 'center',
  width: '90%',
  marginTop: 20,
  marginBottom: 20,
  borderRadius: 10,
  justifyContent: 'center',
  flexDirection: 'column',
};
const imageStyle = {
  width: Dimensions.get('window').width - 70,
  height: undefined,
};

const modalApplyStyle = {
  backgroundColor: Colors.white,
  borderRadius: 25,
  padding: 20,
  height: undefined,
};

export default function Profile({ route, navigation }) {
  const isView = route.params?.isView;
  const userId = route.params?.userId;
  const jobId = route.params?.jobId;
  const isApprove = route.params?.isApprove;
  const applicationApproveId = route.params?.applicationApproveId;

  const { authState, onLogout, loadUser, updateUser } = useAuth();
  const user = authState.user;
  const [documents, setDocuments] = useState([]);
  const [data, setData] = useState(null);
  const [isFetchDataUser, setIsFetchDataUser] = useState(true);

  const [modalApplyVisible, setModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const isTrade = data?.user_type?.includes('Tradesperson');
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchUserDataById = async (userId) => {
    try {
      setIsFetchDataUser(true);
      const userData = await getUserById(userId);
      if (userData?.code === 403) {
        await onLogout();
        navigation.navigate('Login');
      }
      setIsFetchDataUser(false);
      setData(userData);
    } catch (error) {}
  };

  useEffect(() => {
    if (userId) {
      fetchUserDataById(userId);
    } else {
      setData(user);
      setIsFetchDataUser(false);
    }
  }, [user, userId]);

  useEffect(() => {
    const fetchDocuments = async () => {
      const docs = await Promise.all(
        data?.documents?.map(async (item, index) => {
          const response = await axios.head(item);
          const contentType = response.headers['content-type'];
          const isVideo = contentType.startsWith('video/');
          const isImage = contentType.startsWith('image/');
          return { item, isVideo, isImage };
        }) || []
      );
      setDocuments(docs);
    };

    fetchDocuments();
  }, [data?.documents]);

  const renderDocument = ({ item, isVideo, isImage }, index) => (
    <View key={index} style={webViewStyle}>
      {isVideo ? (
        <WebView
          allowsInlineMediaPlayback={true}
          mediaPlaybackRequiresUserAction={true}
          startInLoadingState={true}
          originWhitelist={['*']}
          source={
            isVideo
              ? {
                  html: `
            <video width="100%" height="100%" controls playsinline>
              <source src="${item}" type="video/mp4">
              Your browser does not support the video tag.
            </video>
          `,
                }
              : { uri: item }
          }
        />
      ) : (
        <>
          {isImage ? (
            <View style={imageStyle}>
              <Image
                style={{ width: '100%', height: '100%' }}
                resizeMode="contain"
                source={{
                  uri: item,
                }}
              />
            </View>
          ) : (
            <WebView
              allowsInlineMediaPlayback={true}
              mediaPlaybackRequiresUserAction={true}
              startInLoadingState={true}
              originWhitelist={['*']}
              source={{ uri: item }}
            />
          )}
        </>
      )}
    </View>
  );

  const getEmailReceiver = (emails) => {
    return emails.filter((email) => email !== authState.user.email)[0];
  };

  const onSend = async (message) => {
    try {
      const conversationDetail = await getConversationDetail({
        members: [
          { email: authState.user.email, name: authState.user.user_name },
          { email: data.email, name: data.user_name },
        ],
      });
      await sendMessage({
        conversation_id: conversationDetail?.id,
        unique_email_id: conversationDetail?.unique_email_id,
        message,
        receiver_email: getEmailReceiver(conversationDetail?.user_emails),
      });
    } catch (error) {
      Alert.alert('Fail to send full address, please try again:');
    }
  };

  const handleApprove = async () => {
    try {
      setIsLoading(true);
      try {
        const res = await businessApproveJob({
          jobId: jobId,
          applicationApproveId: applicationApproveId,
        });
        if (res.success) {
          // await onSend(location);
          setIsSuccess(true);
          setIsLoading(false);
        }
      } catch (error) {
        Alert.alert('Error when approve: ' + String(error), '', [
          {
            text: 'OK',
            onPress: () => {
              setIsLoading(false);
            },
          },
        ]);
      }
      setIsLoading(false);
    } catch (err) {}
  };

  const handleDeleteAccount = async () => {
    try {
      setIsDeleting(true);
      const userRes = await updateUser({
        account_status: 'deactive',
      });

      if (userRes.success) {
        Alert.alert('Success', 'Your account has been deleted');
        await onLogout();
        navigation.navigate('Login');
      }
    } catch (error) {
      console.log('Delete account error', error);
      Alert.alert('Error', 'Failed to delete account');
    }
    setIsDeleting(false);
    setIsDeleteModalVisible(false);
  };

  const renderPortfolioPreview = () => {
    if (!data?.portfolio_images?.length) {
      return <CustomText style={text} text="No portfolio images" />;
    }

    return (
      <View>
        <CustomButton
          style={{
            borderRadius: 25,
            marginTop: 15,
            backgroundColor: '#4689D6',
            width: '50%',
            alignSelf: 'center',
          }}
          title="View Portfolio"
          color="white"
          onPress={() => navigation.navigate('Portfolio', { userId: data.id, isView: true })}
        />
      </View>
    );
  };

  const shouldShowBusinessDetails = !isView || (isView && isTrade);

  return (
    <SafeAreaView style={backgroundStyle}>
      <ImageBackground source={BackgroundGradient} style={{ ...backgroundStyle, height: Dimensions.get('window').height }}>
        <Header title="Bio" navigation={navigation} hasBack />
        <View style={styles.container}>
          <View style={profileForm}>
            {isFetchDataUser ? (
              <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <ActivityIndicator size="large" color={Colors.black} />
              </View>
            ) : (
              <>
                <ScrollView
                  onScroll={({ nativeEvent }) => {
                    if (nativeEvent.contentOffset.y <= -100) {
                      setIsFetchDataUser(true);
                      loadUser();
                    }
                  }}
                  scrollEventThrottle={50}
                  showsVerticalScrollIndicator={false}
                  contentContainerStyle={{ flexGrow: 1 }}
                >
                  <View
                    style={{
                      flexDirection: 'column',
                      gap: 20,
                      padding: 20,
                      paddingTop: 30,
                    }}
                  >
                    <View style={titleRow}>
                      <View style={uploadButton}>
                        {data?.avatar_url ? (
                          <View style={uploadedFileContainer}>
                            <Image source={{ uri: data.avatar_url }} style={thumb} resizeMode="cover" />
                          </View>
                        ) : (
                          <Image source={isTrade ? TradesProfileIcon : ProfileIcon} style={{ width: 50, height: 50 }} resizeMode="contain" />
                        )}
                      </View>
                      <View style={{ marginLeft: 20, flex: 1 }}>
                        <CustomText style={formTitle} text={data?.user_name} />
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                          <StarRating
                            disabled={true}
                            maxStars={5}
                            starStyle={{ marginRight: 10 }}
                            rating={data?.review_summary ? JSON.parse(data?.review_summary)?.average_rating : 0}
                            starSize={20}
                            fullStarColor={'#FFD700'}
                            emptyStarColor={'#FFD700'}
                          />
                        </View>
                      </View>
                    </View>

                    {isTrade ? (
                      <>
                        <View>
                          <CustomText style={title} text="Bio" />
                          <CustomText style={text} text={data?.bio || 'No bio provided'} />
                        </View>

                        <View>
                          <CustomText style={title} text="Documentation" />
                          <CustomText style={text} text={data?.documentation || 'No documentation provided'} />
                        </View>

                        <View>
                          <CustomText style={title} text="Qualifications" />
                          <CustomText style={text} text={data?.qualifications || 'No qualifications provided'} />
                        </View>

                        <View>
                          <CustomText style={title} text="Location" />
                          <View style={{ marginLeft: 10 }}>
                            <View style={{ flexDirection: 'row', marginBottom: 5 }}>
                              <CustomText style={{ ...text, fontWeight: 'bold' }} text="City: " />
                              <CustomText style={text} text={data?.city || 'Not specified'} />
                            </View>
                            <View style={{ flexDirection: 'row', marginBottom: 5 }}>
                              <CustomText style={{ ...text, fontWeight: 'bold' }} text="County: " />
                              <CustomText style={text} text={data?.county || 'Not specified'} />
                            </View>
                            <View style={{ flexDirection: 'row' }}>
                              <CustomText style={{ ...text, fontWeight: 'bold' }} text="Travel Distance: " />
                              <CustomText style={text} text={data?.travel_distance ? `${data.travel_distance} miles` : 'Not specified'} />
                            </View>
                          </View>
                        </View>

                        <View>
                          <CustomText style={title} text="Documents" />
                          {documents.map(renderDocument)}
                        </View>

                        <View>
                          <CustomText style={title} text="Portfolio" />
                          {renderPortfolioPreview()}
                        </View>
                      </>
                    ) : (
                      <>
                        <View>
                          <CustomText style={title} text="Contact Details: " />
                          <CustomText style={text} text={data?.company_contact} />
                        </View>
                        <View>
                          <CustomText style={title} text="Company Bio: " />
                          <CustomText style={text} text={data?.bio || 'No company bio provided'} />
                        </View>
                        <View>
                          <CustomText style={title} text="Company Address: " />
                          <CustomText style={text} text={data?.company_address_1} />
                        </View>
                        {shouldShowBusinessDetails && (
                          <>
                            <View>
                              <CustomText style={title} text="Order Number | Reference: " />
                              <CustomText style={text} text={data?.order_number || '-'} />
                            </View>
                            <View>
                              <CustomText style={title} text="Contact Name for Order: " />
                              <CustomText style={text} text={data?.order_contact_name || '-'} />
                            </View>
                            <View>
                              <CustomText style={title} text="Contact Email for Invoicing: " />
                              <CustomText style={text} text={data?.invoicing_contact_email || '-'} />
                            </View>
                          </>
                        )}
                      </>
                    )}

                    {isView ? (
                      <>
                        {isApprove ? (
                          <View style={editButtonStyle}>
                            <CustomButton
                              style={{
                                borderRadius: 25,
                                marginBottom: 10,
                                backgroundColor: 'orange',
                              }}
                              title={'Approve and send confirmation'}
                              color={'black'}
                              onPress={() => setModalVisible(true)}
                            />
                            <CustomButton
                              style={{ borderRadius: 25 }}
                              title={'Message'}
                              color={'black'}
                              onPress={() => {
                                navigation.navigate('Chat', {
                                  members: [
                                    { email: data.email, name: data?.user_name },
                                    { email: user.email, name: user.user_name },
                                  ],
                                  name: data?.user_name,
                                });
                              }}
                            />
                          </View>
                        ) : (
                          <View style={editButtonStyle}>
                            <CustomButton
                              style={{ borderRadius: 25 }}
                              title={'Post review'}
                              color={'black'}
                              onPress={() => navigation.navigate('Reviews', { userId: data?.id, title: data?.user_name, isOwner: !userId })}
                            />
                          </View>
                        )}
                      </>
                    ) : (
                      <View style={editButtonStyle}>
                        <CustomButton
                          style={{ borderRadius: 25, marginBottom: 10 }}
                          title={'Edit Bio'}
                          color={'black'}
                          onPress={() => navigation.navigate('ProfileEdit')}
                        />
                        {isTrade && (
                          <CustomButton
                            style={{ borderRadius: 25, marginBottom: 10 }}
                            title={'Photo Portfolio'}
                            color={'black'}
                            onPress={() => navigation.navigate('Portfolio')}
                          />
                        )}
                        <CustomButton
                          style={{ borderRadius: 25, borderColor: 'red', borderWidth: 1 }}
                          title={'Delete Account'}
                          textStyle={{ color: 'red' }}
                          onPress={() => setIsDeleteModalVisible(true)}
                        />
                      </View>
                    )}
                  </View>
                </ScrollView>
              </>
            )}
          </View>
        </View>
      </ImageBackground>

      {isApprove && (
        <Modal
          style={{ justifyContent: 'center', alignItems: 'center' }}
          isVisible={modalApplyVisible}
          onBackdropPress={() => {
            setModalVisible(!modalApplyVisible);
          }}
        >
          <View style={modalApplyStyle}>
            {isSuccess ? (
              <>
                <Text style={{ fontSize: 18, padding: 10, textAlign: 'center' }}>Trade Approved and Confirmation sent</Text>
                <CustomButton
                  style={{ width: 100, marginTop: 30, borderRadius: 15, alignSelf: 'center' }}
                  title="Close"
                  color="black"
                  onPress={() => {
                    setIsSuccess(false);
                    setModalVisible(!modalApplyVisible);
                    if (navigation.canGoBack()) {
                      navigation.goBack();
                    } else navigation.navigate('Home');
                  }}
                />
              </>
            ) : (
              <>
                <Text style={{ fontSize: 24, marginBottom: 15, textAlign: 'center' }}>Are you sure you want to approve this application?</Text>
                <Text style={{ fontSize: 18, padding: 10, textAlign: 'center' }}>
                  By approving this applicant you are agreeing to the job being available at the date and time advertised. TradeMotion takes no
                  responsibility for payments between yourself and the applicant. Please see our{' '}
                  <Text style={{ color: 'blue' }} onPress={() => Linking.openURL('https://trademotion.vercel.app/terms')}>
                    terms and conditions
                  </Text>{' '}
                  for further information.
                </Text>
                <View style={{ flexDirection: 'row', marginTop: 30, gap: 10, alignItems: 'center', justifyContent: 'space-around' }}>
                  <CustomButton
                    style={{ width: 100, borderRadius: 15 }}
                    title="Cancel"
                    color="black"
                    onPress={() => setModalVisible(!modalApplyVisible)}
                  />
                  <CustomButton
                    style={{ width: 100, borderRadius: 15, backgroundColor: 'orange' }}
                    title="Approve"
                    color="black"
                    isLoading={isLoading}
                    onPress={() => {
                      handleApprove();
                    }}
                  />
                </View>
              </>
            )}
          </View>
        </Modal>
      )}

      <Modal
        style={{ justifyContent: 'center', alignItems: 'center' }}
        isVisible={isDeleteModalVisible}
        onBackdropPress={() => setIsDeleteModalVisible(false)}
      >
        <View style={modalApplyStyle}>
          <Text style={{ fontSize: 24, marginBottom: 15, textAlign: 'center' }}>Are you sure you want to delete your account?</Text>
          <Text style={{ fontSize: 18, padding: 10, textAlign: 'center' }}>This action cannot be undone. Your account will be deactivated.</Text>
          <View style={{ flexDirection: 'row', marginTop: 30, gap: 10, alignItems: 'center', justifyContent: 'space-around' }}>
            <CustomButton style={{ width: 100, borderRadius: 15 }} title="Cancel" color="black" onPress={() => setIsDeleteModalVisible(false)} />
            <CustomButton
              style={{ width: 100, borderRadius: 15, borderColor: 'red', borderWidth: 1 }}
              title="Delete"
              textStyle={{ color: 'red' }}
              isLoading={isDeleting}
              onPress={handleDeleteAccount}
            />
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}
