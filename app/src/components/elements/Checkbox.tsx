import React, { useState } from 'react';
import BouncyCheckbox from 'react-native-bouncy-checkbox';

const Checkbox = ({ isChecked, onPress, disableBuiltInState = true, disabled = false }) => {
  return (
    <BouncyCheckbox
      size={20}
      fillColor={'black'}
      unfillColor="#FFFFFF"
      iconStyle={{ borderColor: 'black' }}
      innerIconStyle={{ borderWidth: 1 }}
      onPress={onPress}
      isChecked={isChecked}
      disableBuiltInState={disableBuiltInState}
      disabled={disabled}
    />
  );
};

export default Checkbox;
