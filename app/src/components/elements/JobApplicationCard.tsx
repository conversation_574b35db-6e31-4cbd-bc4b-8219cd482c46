import { Colors } from 'react-native/Libraries/NewAppScreen';
import { StyleSheet, View, Text, Dimensions, Pressable, TouchableOpacity, Image } from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import AntDesign from 'react-native-vector-icons/AntDesign';
import StarRating from 'react-native-star-rating';

export default function JobApplicationCard({ name, email, qualifications, status, style, rateData, onTouch }) {
  const listItem = {
    backgroundColor: Colors.white,
    borderRadius: 10,
    paddingHorizontal: 20,
    paddingVertical: 15,
    gap: 10,
  };

  const icon = {
    width: 50,
    height: 50,
    marginRight: 15,
  };

  const textStyle = {
    flexDirection: 'Column',
    color: Colors.black,
    fontFamily: 'Montserrat-Regular',
    marginBottom: 5,
  };

  const bold = {
    fontFamily: 'Montserrat-Bold',
  };

  const greeText: any = {
    color: Colors.green,
    fontWeight: '700',
  };
  const renderStatusIcon = () => {
    return (
      <View
        style={{
          width: 50,
          height: 50,
          borderRadius: 25,
          backgroundColor: 'black',
          borderColor: 'white',
          borderWidth: 1,
          marginRight: 15,
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        {status === 'Pending' ? (
          <AntDesign name="clockcircle" color="white" size={30} />
        ) : status === 'Approved' ? (
          <MaterialIcons name="done-all" color="white" size={30} />
        ) : (
          <Ionicons name="exclamation" color="white" size={30} />
        )}
      </View>
    );
  };

  const countingRating = () => {
    if (rateData) {
      let total = 0;
      rateData.map((item) => {
        total += parseInt(item.rate_number);
      });
      return total / rateData.length;
    } else {
      return 0;
    }
  };

  const rateNumber = countingRating();
  return (
    <>
      <TouchableOpacity onPress={onTouch}>
        <View style={listItem}>
          <View style={{ flexDirection: 'row' }}>
            {renderStatusIcon()}
            <View style={{ flex: 1 }}>
              <Text style={[textStyle, bold]}>{name}</Text>
              <View style={{ width: 100, flexDirection: 'row', alignItems: 'center' }}>
                <StarRating disabled={true} fullStarColor="#FFD700" maxStars={5} sty rating={rateNumber} starSize={20} />
                <Text style={{ marginLeft: 10 }}>({rateData ? rateData?.length : 0})</Text>
              </View>
            </View>
          </View>
          <View style={{ ...textStyle }}>
            <Text style={{ fontWeight: 'bold' }}>Skill / Qualifications: </Text>
            <Text style={{ marginTop: 5 }} numberOfLines={2}>
              {qualifications}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    </>
  );
}
