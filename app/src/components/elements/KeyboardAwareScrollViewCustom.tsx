import { useState } from 'react';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

function KeyboardAwareScrollViewCustom({ isEnableScroll = false, children, props }: any) {
  const [enableScroll, setEnableScroll] = useState(false);

  return (
    <KeyboardAwareScrollView
      {...props}
      scrollEnabled={isEnableScroll || enableScroll}
      onKeyboardWillShow={(frames: Object) => {
        setEnableScroll(true);
      }}
      onKeyboardWillHide={(frames: Object) => {
        setEnableScroll(false);
      }}
    >
      {children}
    </KeyboardAwareScrollView>
  );
}
export default KeyboardAwareScrollViewCustom;
