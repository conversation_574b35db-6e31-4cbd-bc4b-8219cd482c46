import React from 'react';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import { View, Text, TouchableOpacity, Linking, Platform, Alert } from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import AntDesign from 'react-native-vector-icons/AntDesign';
import moment from 'moment-timezone';
import * as AddCalendarEvent from 'react-native-add-calendar-event';
import { PERMISSIONS, RESULTS, request } from 'react-native-permissions';
import CustomButton from '../CustomButton';
import MapView, { Marker } from 'react-native-maps';

type IJobsCardProps = {
  userId?: string;
  userName?: string;
  isOwner?: boolean;
  applicationId?: string;
  name: string;
  title: string;
  status: string;
  jobPrice?: string;
  jobDate?: string;
  jobLocation?: string;
  detail: string;
  isGreen?: boolean;
  onPress: any;
  company: any;
  job: any;
  isViewApplication?: boolean;
  navigation?: any;
  isLeftReviewFromTrade?: boolean;
  businessId?: string;
};
const listItem = {
  backgroundColor: Colors.white,
  borderWidth: 3,
  borderRadius: 10,
  padding: 20,
  flexDirection: 'column' as const,
};

const textStyle = {
  color: Colors.black,
  fontFamily: 'Montserrat-Regular',
  marginBottom: 5,
};

const bold = {
  fontFamily: 'Montserrat-Bold',
};

const greeText: any = {
  color: Colors.green,
  fontWeight: '700',
};

export default function JobsCard({
  name,
  title,
  status,
  jobPrice,
  jobDate,
  jobLocation,
  detail,
  isGreen = false,
  onPress,
  job,
  isViewApplication,
  navigation,
  userId,
  userName,
  isOwner,
  applicationId,
  isLeftReviewFromTrade,
  businessId,
  company,
}: IJobsCardProps) {
  const handleSetReminder = async () => {
    try {
      const permissionWriteOnly = await request(Platform.OS === 'ios' ? PERMISSIONS.IOS.CALENDARS_WRITE_ONLY : PERMISSIONS.ANDROID.WRITE_CALENDAR);
      const permissionFull = await request(Platform.OS === 'ios' ? PERMISSIONS.IOS.CALENDARS : PERMISSIONS.ANDROID.WRITE_CALENDAR);
      if (permissionWriteOnly !== RESULTS.GRANTED && permissionFull !== RESULTS.GRANTED) {
        Alert.alert('Permission required', 'Please enable calendar permission in settings');
        return;
      }

      const eventConfig = {
        title: `TM ${title}`,
        location: jobLocation || 'No location provided',
        startDate: new Date(jobDate).toISOString(),
        endDate: new Date(jobDate).toISOString(),
        notes: 'You have a job reminder today!',
        navigationBarIOS: {
          barTintColor: 'orange',
          tintColor: 'green',
          translucent: false,
          backgroundColor: 'green',
          titleColor: 'blue',
        },
      };
      const eventId = await AddCalendarEvent.presentEventCreatingDialog(eventConfig);
      if (eventId) {
        console.log(`Event created with id: ${eventId}`);
      } else {
        console.log('Event not created');
      }
    } catch (error) {
      console.log('handleSetReminder error', error);
      Alert.alert('Error', 'Could not add to calendar.');
    }
  };

  const renderStatusIcon = () => {
    return (
      <View
        style={{
          width: 50,
          height: 50,
          borderRadius: 25,
          backgroundColor: 'black',
          borderColor: 'white',
          borderWidth: 1,
          marginRight: 15,
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        {status === 'Pending' ? (
          <AntDesign name="clockcircle" color="white" size={30} />
        ) : status === 'Approved' ? (
          <MaterialIcons name="done-all" color="white" size={30} />
        ) : status === 'Completed' ? (
          <Ionicons name="checkmark-circle" color="white" size={30} />
        ) : status === 'Rejected' ? (
          <Ionicons name="close-circle" color="white" size={30} />
        ) : (
          <Ionicons name="exclamation" color="white" size={30} />
        )}
      </View>
    );
  };

  const openMaps = () => {
    const url = Platform.select({
      ios: `maps:0,0?q=${job.location_lat},${job.location_long}`,
      android: `geo:0,0?q=${job.location_lat},${job.location_long}`,
    });
    Linking.openURL(url);
  };
  console.log('job', job);

  return (
    <>
      {isViewApplication ? (
        <View
          style={Object.assign(
            {},
            listItem,
            status === 'Approved' ? { borderColor: '#32CD32' } : {},
            status === 'Completed' || status === 'Rejected' ? { backgroundColor: '#808080', borderColor: '#808080' } : {}
          )}
        >
          <View style={{ flexDirection: 'row' as const }}>
            {renderStatusIcon()}
            <View style={{ flex: 1 }}>
              <TouchableOpacity onPress={() => navigation.navigate('Profile', { isView: true, userId: businessId })}>
                <Text style={[textStyle, bold]}>{name}</Text>
              </TouchableOpacity>
              <Text style={textStyle}>{title}</Text>
              <View style={{ flexDirection: 'row' as const }}>
                <Text style={textStyle}>£{jobPrice}/h - </Text>
                <Text style={{ ...textStyle, ...(status === 'Approved' ? { color: '#FF0000' } : {}) }}>
                  {moment.utc(job?.start_date_required).local().format('DD MMM YYYY hh:ssa')}
                </Text>
              </View>
              {status === 'Approved' ? (
                <>
                  <TouchableOpacity
                    onPress={() => {
                      isViewApplication &&
                        navigation.navigate('MapViewJob', {
                          job: job,
                        });
                    }}
                    style={{ flexDirection: 'row' as const, alignItems: 'center' }}
                  >
                    <Text style={textStyle}>{job?.location}</Text>
                  </TouchableOpacity>
                  {job.location_lat && job.location_long && (
                    <View style={{ marginTop: 8, marginBottom: 8 }}>
                      <MapView
                        style={{ width: '100%', height: 120, borderRadius: 10 }}
                        initialRegion={{
                          latitude: parseFloat(job.location_lat),
                          longitude: parseFloat(job.location_long),
                          latitudeDelta: 0.005,
                          longitudeDelta: 0.005,
                        }}
                        scrollEnabled={false}
                        zoomEnabled={false}
                        pitchEnabled={false}
                        rotateEnabled={false}
                        pointerEvents="none"
                      >
                        <Marker
                          coordinate={{
                            latitude: parseFloat(job.location_lat),
                            longitude: parseFloat(job.location_long),
                          }}
                          title={job.title}
                          description={job.location}
                        />
                      </MapView>
                      <TouchableOpacity onPress={openMaps} style={{ marginTop: 6 }}>
                        <Text style={{ color: 'blue', textDecorationLine: 'underline' }}>Open in Maps</Text>
                      </TouchableOpacity>
                    </View>
                  )}
                  <CustomButton
                    title="View Job Details"
                    onPress={() => navigation.navigate('ApprovedJobDetail', { job, company })}
                    color="black"
                    style={{ marginTop: 10, width: '50%' }}
                  />
                </>
              ) : (
                <Text style={textStyle}>{job?.short_location}</Text>
              )}
            </View>
          </View>
          {status === 'Approved' && userId && userName && isOwner !== undefined && (
            <View style={{ flexDirection: 'row' as const, alignItems: 'center', justifyContent: 'space-between', marginTop: 20 }}>
              <CustomButton
                style={{ width: 150, borderRadius: 10, minHeight: 60 }}
                title={isLeftReviewFromTrade ? 'Reviewed' : 'Complete + Leave Review'}
                disabled={isLeftReviewFromTrade}
                color="black"
                backgroundColor={isLeftReviewFromTrade ? '#808080' : Colors.black}
                onPress={() => {
                  navigation.navigate('Reviews', { userId, title: userName, isOwner: isOwner, applicationId: applicationId });
                }}
              ></CustomButton>
              <CustomButton
                style={{ width: 150, borderRadius: 10, minHeight: 60 }}
                title="Add to calendar"
                color="black"
                onPress={handleSetReminder}
              ></CustomButton>
            </View>
          )}
        </View>
      ) : (
        <TouchableOpacity activeOpacity={0.9} style={Object.assign({}, listItem)} onPress={onPress}>
          {renderStatusIcon()}
          <View style={{ flex: 1 }}>
            <Text style={[textStyle, bold]}>{name}</Text>
            <Text style={textStyle}>{title}</Text>
            <Text style={[textStyle, isGreen ? greeText : {}]}>{detail}</Text>
          </View>
        </TouchableOpacity>
      )}
    </>
  );
}
