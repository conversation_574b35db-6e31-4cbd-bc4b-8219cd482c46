import { MultiSelect } from 'react-native-element-dropdown';
import { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import Entypo from 'react-native-vector-icons/Entypo';

const MultiSelectComponent = ({ data, value, placeholder, onSelectItem }) => {
  const [selected, setSelected] = useState([]);
  const renderItem = (item, selected) => {
    return (
      <View style={{ padding: 10 }}>
        <Text>{item.label}</Text>
        {selected && (
          <View style={styles.selectedItemIcon}>
            <Entypo name="check" size={20} color="black" />
          </View>
        )}
      </View>
    );
  };

  useEffect(() => {
    value ? setSelected(value) : setSelected([]);
  }, [value]);

  return (
    <View>
      <MultiSelect
        style={styles.input}
        selectedTextStyle={styles.selectedStyle}
        data={data}
        labelField="label"
        valueField="value"
        placeholder={placeholder || 'Select..'}
        value={selected}
        search
        searchPlaceholder="Search..."
        onChange={(item) => {
          setSelected(item);
          onSelectItem(item);
        }}
        renderItem={renderItem}
        renderSelectedItem={(item, unSelect) => (
          <TouchableOpacity onPress={() => unSelect && unSelect(item)}>
            <View style={styles.selectedStyle}>
              <Text style={styles.textSelectedStyle}>{item.label}</Text>
              <Entypo name="trash" size={12} color="rgba(0,0,0,0.3)" />
            </View>
          </TouchableOpacity>
        )}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  selectedStyle: {
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 4,
    padding: 5,
    margin: 5,
    flexDirection: 'row',
    alignItems: 'center',
    columnGap: 5,
  },
  textSelectedStyle: {
    fontSize: 12,
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.gray,
    marginTop: 10,
    height: 40,
    paddingHorizontal: 10,
    fontFamily: 'Montserrat-Regular',
  },
  errors: {
    color: 'red',
    marginTop: 4,
  },
  selectedItemIcon: {
    position: 'absolute',
    top: 0,
    right: 10,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default MultiSelectComponent;
