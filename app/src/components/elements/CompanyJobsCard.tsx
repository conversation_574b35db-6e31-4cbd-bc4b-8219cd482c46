import React from 'react';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import { View, Text, TouchableOpacity, Dimensions } from 'react-native';
import { useState } from 'react';
import CustomButton from '../CustomButton';
import { useNavigation } from '@react-navigation/native';

export default function JobsCard({
  trade,
  task,
  price,
  jobStatus,
  onPress,
  contactName,
  siteName,
  numberOfApplication,
  isLeftReviewFromTrade,
  userId,
  applicationId,
  jobId,
  navigation,
}) {
  const [isViewMore, setIsViewMore] = useState(false);
  const isCompleted = jobStatus[0] === 'Completed';
  const isApproved = jobStatus[0] === 'Closed';
  return (
    <View
      style={[
        container,
        { position: 'relative' },
        isCompleted ? { borderWidth: 3, borderColor: '#0066CC' } : isApproved ? { borderWidth: 3, borderColor: '#32CD32' } : {},
      ]}
    >
      <View
        style={{
          position: 'absolute',
          right: 10,
          top: 10,
          borderWidth: 1,
          borderColor: 'red',
          width: 20,
          height: 20,
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: 50,
          backgroundColor: 'red',
        }}
      >
        <Text style={{ color: Colors.white, fontSize: 12 }} numberOfLines={1}>
          {numberOfApplication}
        </Text>
      </View>
      <TouchableOpacity activeOpacity={0.9} style={[listItem]} onPress={onPress}>
        <View>
          {isApproved && (
            <Text style={[textStyle, bold, { width: Dimensions.get('window').width - 90 }]} numberOfLines={1}>
              Applicant approved and confirmation sent
            </Text>
          )}
          {isCompleted && (
            <Text style={[textStyle, bold, { color: '#0066CC' }]} numberOfLines={1}>
              Job completed
            </Text>
          )}
          <View style={{ flexDirection: 'row', alignItems: 'center', width: Dimensions.get('window').width - 100 }}>
            <Text style={[textStyle, bold, { flexShrink: 1 }]} numberOfLines={1} ellipsizeMode="tail">
              {trade.join(', ')}
            </Text>
          </View>
          <Text style={[textStyle, bold]}>{task}</Text>
          {/* <Text style={textStyle}>{description}</Text> */}
          <Text style={textStyle}>{siteName}</Text>
          {isViewMore && (
            <View>
              <Text style={textStyle}>{contactName}</Text>
              <Text style={textStyle}>{price}</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingHorizontal: 20, marginTop: 10 }}>
        {isLeftReviewFromTrade && isApproved && (
          <CustomButton
            onPress={() =>
              navigation.navigate('Reviews', {
                userId: userId,
                title: null,
                isOwner: false,
                applicationId: applicationId,
                jobId: jobId,
              })
            }
            style={{ width: 150, borderRadius: 10 }}
            title="Leave review"
            color="black"
          />
        )}
        <TouchableOpacity style={seeMoreStyle} onPress={() => setIsViewMore(!isViewMore)}>
          <Text>{isViewMore ? 'See less' : 'View more '}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const listItem: any = {
  padding: 20,
  paddingBottom: 0,
  flexDirection: 'row',
};

const textStyle = {
  color: Colors.black,
  fontFamily: 'Montserrat-Regular',
  marginBottom: 5,
};

const bold = {
  fontFamily: 'Montserrat-Bold',
};

const container = {
  position: 'relative',
  borderRadius: 10,
  backgroundColor: Colors.white,
  paddingBottom: 10,
};

const seeMoreStyle = {
  position: 'absolute',
  bottom: 10,
  right: 10,
};
