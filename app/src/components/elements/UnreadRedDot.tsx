import { StyleProp, View, ViewStyle } from 'react-native';

export interface UnreadRedDotProps {
  style: StyleProp<ViewStyle>;
}

/**
 * A red dot indicating that a item is unread.
 *
 * @param {{ style: StyleProp<ViewStyle> }} props
 * @prop {StyleProp<ViewStyle>} style The style of the View component.
 * @returns {React.ReactElement} A red dot indicating that a item is unread.
 */
const UnreadRedDot: React.FC<UnreadRedDotProps> = ({ style }) => {
  const redDotStyle: StyleProp<ViewStyle> = { width: 8, height: 8, borderRadius: 8, backgroundColor: 'red', ...(style as object) };
  return (
    <>
      <View style={redDotStyle} />
    </>
  );
};

export default UnreadRedDot;
