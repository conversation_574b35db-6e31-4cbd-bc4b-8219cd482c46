import { NativeModules, NativeEventEmitter, Platform } from 'react-native';
import { useEffect } from 'react';

const RNOneSignal = NativeModules.OneSignal;
var oneSignalEventEmitter;
var _eventTypeHandler = new Map();

function handleEventBroadcast(type, broadcast) {
    if (!oneSignalEventEmitter || !oneSignalEventEmitter.addListener) return;
    return oneSignalEventEmitter.addListener(
        broadcast, (notification) => {
            var handler = _eventTypeHandler.get(type);
            if (handler) {
                handler(notification);
            }
        }
    );
}

// we need to wrap it so it will work for ver 3.4.2 and ver 5+
export class OneSignalWrapper {

    static init(appId: string) {
        let version;
        if (typeof RNOneSignal.init === 'function' && typeof RNOneSignal.initWithAppId === 'function') {
            // ver 3.4.2
            version = '3.4.2';
            if (Platform.OS === 'ios') RNOneSignal.init(appId);
            else RNOneSignal.init(appId);
        } else if (typeof RNOneSignal.initialize === 'function') {
            // ver 5
            version  = '5.x';
            RNOneSignal.initialize(appId);
        }
        oneSignalEventEmitter = new NativeEventEmitter(RNOneSignal);
        try {
            if (version === '3.4.2') handleEventBroadcast('opened', 'OneSignal-remoteNotificationOpened');
        } catch(err) {}
        try {
            if (version === '5.x') handleEventBroadcast('opened', 'OneSignal-notificationClicked');
        } catch(err) {}
    }

    static requestPermission() {
        if (typeof RNOneSignal.requestNotificationPermission === 'function') {
            // ver 5
            RNOneSignal.requestNotificationPermission(true);
        } 
        if (typeof RNOneSignal.promptForPushNotificationPermissions === 'function') {
            // ver 3.4.2
            RNOneSignal.promptForPushNotificationPermissions(function(){});
        }
    }

    static setEmail(email) {
        if (typeof RNOneSignal.setUnauthenticatedEmail === 'function') {
            RNOneSignal.setUnauthenticatedEmail(email, function(){});
        }
        if (typeof RNOneSignal.addEmail === 'function') {
            RNOneSignal.addEmail(email);
        }
        if (typeof RNOneSignal.sendTag === 'function') {
            RNOneSignal.sendTag('user_email', email);
        }
        if (typeof RNOneSignal.addTag === 'function') {
            RNOneSignal.addTag('user_email', email);
        }
    }

    static logoutEmail() {
        if (typeof RNOneSignal.logoutEmail === 'function') {
            RNOneSignal.logoutEmail(function(){});
        }
        if (typeof RNOneSignal.removeEmail === 'function') {
            RNOneSignal.removeEmail();
        }
        if (typeof RNOneSignal.deleteTag === 'function') {
            RNOneSignal.deleteTag('user_email');
        }
        if (typeof RNOneSignal.removeTags === 'function') {
            RNOneSignal.removeTags(['user_email']);
        }
        if (typeof RNOneSignal.logout === 'function') {
            RNOneSignal.logout();
        }
    }

    static onNotiOpen(handler) {
        if (typeof RNOneSignal.initNotificationOpenedHandlerParams === 'function') {
            RNOneSignal.initNotificationOpenedHandlerParams();
            _eventTypeHandler.set('opened', handler);
        }
        if (typeof RNOneSignal.addNotificationClickListener === 'function') {
            RNOneSignal.addNotificationClickListener();
            _eventTypeHandler.set('opened', handler);
        }
    }
}

const NotificationInit = () => {
    useEffect(() => {
        if (!RNOneSignal) return;
        
        OneSignalWrapper.init("************************************");
        OneSignalWrapper.requestPermission();
        OneSignalWrapper.onNotiOpen(data => {
            console.log('ON NOTI OPEN', data);
        })
    }, []);
    return null;
};

export default NotificationInit;