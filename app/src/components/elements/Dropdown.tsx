import { Dropdown } from 'react-native-element-dropdown';
import { useEffect, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import Entypo from 'react-native-vector-icons/Entypo';

const DropdownComponent = ({ data, value, placeholder, onSelectItem, disabled }) => {
  const [selected, setSelected] = useState([]);
  const renderItem = (item, selected) => {
    return (
      <View style={{ padding: 10 }}>
        <Text>{item.label}</Text>
        {selected && (
          <View style={styles.selectedItemIcon}>
            <Entypo name="check" size={20} color="black" />
          </View>
        )}
      </View>
    );
  };

  useEffect(() => {
    value ? setSelected(value) : setSelected([]);
  }, [value]);

  return (
    <View>
      <Dropdown
        disable={disabled}
        style={{ ...styles.input, backgroundColor: disabled ? Colors.lighter : 'white' }}
        selectedTextStyle={styles.selectedStyle}
        data={data}
        labelField="label"
        valueField="value"
        placeholder={placeholder || 'Select..'}
        value={selected}
        search
        searchPlaceholder="Search..."
        onChange={(item) => {
          setSelected(item);
          onSelectItem(item.value);
        }}
        selectedTextProps={{ numberOfLines: 1, style: { backgroundColor: 'white' } }}
        renderItem={renderItem}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  selectedStyle: {
    backgroundColor: '#eee',
    borderRadius: 4,
    padding: 5,
    margin: 5,
    flexDirection: 'row',
    alignItems: 'center',
    columnGap: 5,
  },
  textSelectedStyle: {
    fontSize: 12,
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.gray,
    marginTop: 10,
    height: 40,
    paddingHorizontal: 10,
    fontFamily: 'Montserrat-Regular',
  },
  errors: {
    color: 'red',
    marginTop: 4,
  },
  selectedItemIcon: {
    position: 'absolute',
    top: 0,
    right: 10,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default DropdownComponent;
