import React, { useEffect, useState } from 'react';
import { StyleSheet, Dimensions, ActivityIndicator, Alert, ImageBackground, TextInput } from 'react-native';

import { ScrollView } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import Header from './Header';
import { View } from 'react-native';
import CustomButton from './CustomButton';
import CustomText from './CustomText';
import { BackgroundGradient } from '../assets/image';
import { useAuth } from '../../context/AuthContext';
import { readReview, useCreateReview, useListReviews } from '../services/reviewsService';
import StarRating from 'react-native-star-rating';
import { useAtom } from 'jotai';
import { buttonNumbersAtom } from '../atoms/buttonNumbersAtom';
import { getUserById } from '../services/usersService';

export default function Reviews({ route, navigation }) {
  const { authState, onLogout } = useAuth();
  const { userId, title, isOwner, applicationId, jobId, leaveReview } = route.params;
  const [titleName, setTitleName] = useState(title || '');
  const [reviewText, setReviewText] = React.useState('');
  const [reviewRate, setReviewRate] = React.useState(0);
  const ownUser = authState.user.id;
  const [buttonNumbers, setButtonNumbers] = useAtom(buttonNumbersAtom);
  const { list, isFetching, reFetch } = useListReviews(userId);

  const { isCreating, createReview } = useCreateReview();
  const [isLeftReviewFromTrade, setIsLeftReviewFromTrade] = useState(false);
  // const isOwnerExitedInListReview = list.find((item) => {
  //   return item?.user_review[0].id === ownUser;
  // });

  const getTitleName = async () => {
    if (!title) {
      const userData = await getUserById(userId);
      setTitleName(userData?.user_name);
    }
  };
  useEffect(() => {
    if (!title) {
      getTitleName();
    }
  }, [title]);
  const isShowReviewForm = !isOwner && !isLeftReviewFromTrade && !leaveReview;

  useEffect(() => {
    let unreadReviewIds = list.filter((item) => !item.review_read_status || item.review_read_status.includes('unread')).map((item) => item.id);

    if (unreadReviewIds && unreadReviewIds.length > 0) {
      readReview({ review_ids: unreadReviewIds });
      setButtonNumbers({ ...buttonNumbers, profile: 0 });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [list]);

  const handleSubmitReview = async () => {
    if (reviewRate === 0) {
      Alert.alert('Please rate the review');
      return;
    }
    const dataSubmit = {
      rate_number: reviewRate,
      review_text: reviewText,
      review_recipient: userId,
      user_review: ownUser,
      application_id: applicationId,
      job_id: jobId,
    };
    const dataReview = await createReview(dataSubmit);
    if (dataReview && dataReview.success) {
      reFetch();
      setReviewRate(0);
      setReviewText('');
      setIsLeftReviewFromTrade(true);
      Alert.alert('Review submitted');
    } else if (dataReview && dataReview.code === '403') {
      // logout and redirect to login page
      await onLogout();
      // Navigation will happen after logout completes
      navigation.navigate('Login');
    } else {
      Alert.alert(`Something went wrong: ${dataReview?.message}`);
    }
  };

  return (
    <SafeAreaView style={styles.backgroundStyle}>
      <ImageBackground source={BackgroundGradient} style={[styles.backgroundStyle, { height: Dimensions.get('window').height }]}>
        <Header title="Reviews" navigation={navigation} hasBack />
        <View style={styles.container}>
          <View style={styles.reviewsForm}>
            <View style={styles.titleContainer}>
              <CustomText text={`${titleName} reviews`} isBold={true} />
            </View>

            {isShowReviewForm && (
              <View>
                <View style={styles.breakLine} />
                <CustomText style={styles.reviewTitle} text={`Review: `} />
                <View style={styles.starContainer}>
                  <StarRating
                    selectedStar={(rating) => {
                      setReviewRate(rating);
                    }}
                    fullStarColor="#FFD700"
                    maxStars={5}
                    rating={reviewRate}
                    starSize={20}
                  />
                </View>
                <TextInput style={[styles.input, styles.textArea]} value={reviewText} onChangeText={setReviewText} multiline={true} />
                <CustomButton isLoading={isCreating} onPress={handleSubmitReview} style={styles.submitButton} title="Submit" to="" color="black" />
                <View style={styles.breakLine} />
              </View>
            )}
            <ScrollView>
              <View style={styles.formTable}>
                {isFetching ? (
                  <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                    <ActivityIndicator size="large" color={Colors.black} />
                  </View>
                ) : (
                  <>
                    {list.length === 0 ? (
                      <CustomText style={{ textAlign: 'center', fontSize: 22 }} text={'No reviews display here'} color="white" />
                    ) : (
                      <>
                        {list.map((item, index) => {
                          return (
                            <View key={index} style={styles.tableRow}>
                              <View style={styles.rowTitle}>
                                <CustomText numberOfLines={1} text={item?.user_review[0].user_name} isBold={true} />
                              </View>
                              <View style={styles.rowCell}>
                                <StarRating on disabled={true} fullStarColor="#FFD700" maxStars={5} sty rating={item?.rate_number} starSize={20} />
                              </View>
                              <View style={styles.rowText}>
                                <CustomText numberOfLines={3} text={item?.review_text} />
                              </View>
                            </View>
                          );
                        })}
                      </>
                    )}
                  </>
                )}
              </View>
            </ScrollView>
          </View>
        </View>
      </ImageBackground>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  backgroundStyle: {
    backgroundColor: Colors.black,
    width: '100%',
    height: '100%',
  },
  container: {
    marginTop: 32,
    paddingHorizontal: 16,
  },
  reviewsForm: {
    backgroundColor: Colors.white,
    marginTop: 16,
    borderRadius: 25,
    padding: 20,
    flexDirection: 'column',
    gap: 20,
    height: Dimensions.get('window').height - 200,
  },
  titleContainer: {
    borderBottomWidth: 1,
    paddingBottom: 10,
    borderColor: 'rgba(0,0,0,0.2)',
  },
  breakLine: {
    borderBottomColor: Colors.gray,
    width: Dimensions.get('window').width - 200,
    borderBottomWidth: 1,
    marginTop: 10,
  },
  reviewTitle: {
    fontWeight: 'bold',
    fontSize: 18,
    marginTop: 10,
  },
  starContainer: {
    width: 250,
    marginVertical: 10,
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.gray,
    marginTop: 10,
    height: 40,
    paddingHorizontal: 10,
    fontFamily: 'Montserrat-Regular',
  },
  textArea: {
    height: 70,
    borderRadius: 5,
    paddingVertical: 20,
  },
  submitButton: {
    marginVertical: 10,
  },
  formTable: {
    alignItems: 'center',
    justifyContent: 'center',
    gap: 20,
  },
  tableRow: {
    alignSelf: 'stretch',
    flexDirection: 'column',
  },
  rowTitle: {
    flex: 1,
    alignSelf: 'stretch',
  },
  rowText: {
    flex: 1,
    marginTop: 5,
    marginLeft: 15,
  },
  rowCell: {
    flex: 1,
    alignSelf: 'stretch',
    flexDirection: 'row',
  },
});
