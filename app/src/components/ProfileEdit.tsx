import React, { useEffect } from 'react';
import DocumentPicker from 'react-native-document-picker';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import Header from './Header';
import { View, Text, TextInput, Image, Dimensions, TouchableOpacity, Alert, ImageBackground } from 'react-native';
import CustomText from './CustomText';
import { useAuth } from '../../context/AuthContext';
const styles = require('../../style/style');
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import StarRating from 'react-native-star-rating';
import KeyboardAwareScrollViewCustom from './elements/KeyboardAwareScrollViewCustom';
import CustomButton from './CustomButton';
import { uploadFile } from '../services/fileUploadService';
import FilePicker from './elements/FilePicker';
import _ from 'lodash';
import { launchImageLibrary } from 'react-native-image-picker';
import { BackgroundGradient, ProfileIcon, TradesProfileIcon } from '../assets/image';
import { checkExitOrderNumber } from '../services/usersService';

type TFile = {
  fileCopyUri?: string;
  name: string;
  size: number;
  type: string;
  uri: string;
  isLocal?: boolean;
};

const backgroundStyle = {
  backgroundColor: Colors.black,
  width: '100%',
  height: '100%',
};

const profileForm = {
  backgroundColor: Colors.white,
  borderRadius: 25,
  flexDirection: 'column',
  height: Dimensions.get('window').height - 205,
  gap: 20,
};

const formTitle = {
  fontSize: 24,
  flex: 1,
  color: Colors.black,
  fontFamily: 'Montserrat-Regular',
};

const title = {
  fontSize: 20,
};

const titleRow = {
  flexDirection: 'row',
  alignItems: 'center',
  borderBottomColor: Colors.gray,
  paddingBottom: 10,
  borderBottomWidth: 1,
};

const uploadButton = {
  padding: 5,
  flexDirection: 'row',
  justifyContent: 'center',
  alignItems: 'center',
  columnGap: 5,
  backgroundColor: 'white',
  borderRadius: 100,
  height: 100,
  width: 100,
  borderWidth: 1,
  borderStyle: 'dashed',
  borderColor: 'rgba(0,0,0,1)',
};

const uploadedFileContainer = {
  flexDirection: 'row',
  alignItems: 'flex-start',
  justifyContent: 'flex-start',
  backgroundColor: 'rgba(0,0,0,0.1)',
  borderRadius: 100,
  margin: 5,
  height: 100,
  width: 100,
  borderColor: 'rgba(0,0,0,0.1)',
};
const thumb = {
  aspectRatio: 1,
  borderRadius: 100,
  height: 100,
  width: 100,
  overflow: 'hidden',
  backgroundColor: 'transparent',
  justifyContent: 'center',
  alignItems: 'center',
};
const trashButton = {
  position: 'absolute',
  width: 20,
  height: 20,
  borderRadius: 30,
  top: 0,
  right: 0,
  backgroundColor: '#eeeee0',
  shadowColor: '#000',
  shadowOffset: {
    width: 0,
    height: 2,
  },
  shadowOpacity: 0.25,
  shadowRadius: 3.84,
  elevation: 5,
  padding: 5,
  alignItems: 'center',
  justifyContent: 'center',
};

const editButtonStyle = {
  position: 'absolute',
  bottom: 20,
  flex: 1,
  alignSelf: 'center',
  width: '90%',
  marginTop: 20,
  borderRadius: 10,
};

const styleInput = {
  borderWidth: 1,
  borderColor: Colors.gray,
  marginTop: 10,
  height: 40,
  paddingHorizontal: 10,
  fontFamily: 'Montserrat-Regular',
};

export default function ProfileEdit({ navigation }) {
  const { authState, updateUser } = useAuth();
  const user = authState.user;
  const isTrade = authState.isTrade;
  const [file, setFile] = React.useState(user?.avatar_url);
  const [qualifications, setQualifications] = React.useState('');
  const [companyContact, setCompanyContact] = React.useState('');
  const [companyAddress1, setCompanyAddress1] = React.useState('');
  const [documentFiles, setDocumentFiles] = React.useState([]);
  const [isLoading, setIsLoading] = React.useState(false);
  const [initData, setInitData] = React.useState(null);
  const [orderNumber, setOrderNumber] = React.useState('');
  const [orderContactName, setOrderContactName] = React.useState('');
  const [invoicingContactEmail, setInvoicingContactEmail] = React.useState('');
  const [bio, setBio] = React.useState('');
  const [documentation, setDocumentation] = React.useState('');
  const [city, setCity] = React.useState('');
  const [county, setCounty] = React.useState('');
  const [travelDistance, setTravelDistance] = React.useState('');

  useEffect(() => {
    user.qualifications && setQualifications(user.qualifications);
    user.documents && setDocumentFiles(user.documents);
    user?.company_contact && setCompanyContact(user?.company_contact);
    user?.company_address_1 && setCompanyAddress1(user?.company_address_1);
    user?.order_number && setOrderNumber(user?.order_number);
    user?.order_contact_name && setOrderContactName(user?.order_contact_name);
    user?.invoicing_contact_email && setInvoicingContactEmail(user?.invoicing_contact_email);
    user?.bio && setBio(user?.bio);
    user?.documentation && setDocumentation(user?.documentation);
    user?.city && setCity(user?.city);
    user?.county && setCounty(user?.county);
    user?.travel_distance && setTravelDistance(user?.travel_distance);
    setInitData({
      avatar_url: user.avatar_url,
      qualifications: user.qualifications,
      documents: user.documents,
      company_contact: user.company_contact,
      company_address_1: user.company_address_1,
      order_number: user.order_number,
      order_contact_name: user.order_contact_name,
      invoicing_contact_email: user.invoicing_contact_email,
      bio: user.bio,
      documentation: user.documentation,
      city: user.city,
      county: user.county,
      travel_distance: user.travel_distance,
    });
  }, [authState]);

  const openPicker = () => {
    const options: any = {
      mediaType: 'photo',
    };

    launchImageLibrary(options, (response) => {
      if (response.didCancel) {
        console.log('User cancelled image picker');
      } else if (response.errorMessage) {
        console.log('ImagePicker Error: ', response.errorMessage);
      } else {
        const source = response.assets[0];
        setFile({
          fileCopyUri: null,
          name: source.fileName,
          size: source.fileSize,
          type: source.type,
          uri: source.uri,
          isLocal: true,
        });
      }
    });
  };
  const renderThumb = (file: TFile) => {
    if (file) {
      if (typeof file === 'string') {
        return (
          <View style={thumb}>
            <Image source={{ uri: file }} style={{ height: '100%', width: '100%' }} />
          </View>
        );
      } else {
        return (
          <View style={thumb}>
            <Image source={{ uri: file.uri }} style={{ height: '100%', width: '100%' }} />
          </View>
        );
      }
    }
  };

  const handleProfileEditButton = async () => {
    let avatarUrl = file;
    setIsLoading(true);
    if (!_.isEqual(orderNumber, initData.order_number)) {
      const orderNumberCheck = await checkExitOrderNumber(orderNumber);
      if (orderNumberCheck.success) {
        Alert.alert('Error', 'Order number already exists');
        setIsLoading(false);
        return;
      }
    }

    try {
      if (file && typeof file === 'object') {
        const formData = new FormData();
        formData.append('file', file);
        const data = await uploadFile(formData);
        avatarUrl = data.link;
      }

      const documents = await Promise.all(
        documentFiles.map(async (file) => {
          if (typeof file === 'string') {
            return file;
          } else {
            const formData = new FormData();
            formData.append('file', file);
            const data = await uploadFile(formData);
            return data.link;
          }
        })
      );
      setDocumentFiles(documents);
      console.log('payload', {
        avatar_url: file === null ? null : avatarUrl,
        ...(!_.isEqual(qualifications, initData.qualifications) && { qualifications: qualifications }),
        ...(!_.isEqual(companyContact, initData.company_contact) && { company_contact: companyContact }),
        ...(!_.isEqual(companyAddress1, initData.company_address_1) && { company_address_1: companyAddress1 }),
        ...(!_.isEqual(orderNumber, initData.order_number) && { order_number: orderNumber }),
        ...(!_.isEqual(orderContactName, initData.order_contact_name) && { order_contact_name: orderContactName }),
        ...(!_.isEqual(invoicingContactEmail, initData.invoicing_contact_email) && { invoicing_contact_email: invoicingContactEmail }),
        ...(!_.isEqual(bio, initData.bio) && { bio: bio }),
        ...(!_.isEqual(documentation, initData.documentation) && { documentation: documentation }),
        ...(!_.isEqual(city, initData.city) && { city: city }),
        ...(!_.isEqual(county, initData.county) && { county: county }),
        ...(!_.isEqual(travelDistance, initData.travel_distance) && { travel_distance: travelDistance }),
        documents: !_.isEmpty(documents) ? documents : documentFiles,
      });

      const userRes = await updateUser({
        avatar_url: file === null ? '' : avatarUrl,
        ...(!_.isEqual(qualifications, initData.qualifications) && { qualifications: qualifications }),
        ...(!_.isEqual(companyContact, initData.company_contact) && { company_contact: companyContact }),
        ...(!_.isEqual(companyAddress1, initData.company_address_1) && { company_address_1: companyAddress1 }),
        ...(!_.isEqual(orderNumber, initData.order_number) && { order_number: orderNumber }),
        ...(!_.isEqual(orderContactName, initData.order_contact_name) && { order_contact_name: orderContactName }),
        ...(!_.isEqual(invoicingContactEmail, initData.invoicing_contact_email) && { invoicing_contact_email: invoicingContactEmail }),
        ...(!_.isEqual(bio, initData.bio) && { bio: bio }),
        ...(!_.isEqual(documentation, initData.documentation) && { documentation: documentation }),
        ...(!_.isEqual(city, initData.city) && { city: city }),
        ...(!_.isEqual(county, initData.county) && { county: county }),
        ...(!_.isEqual(travelDistance, initData.travel_distance) && { travel_distance: travelDistance }),
        documents: !_.isEmpty(documents) ? documents : documentFiles,
      });

      if (userRes.success) {
        Alert.alert('Success', 'User updated successfully');
        navigation.goBack();
      }
    } catch (error) {
      console.log('Update user error', error);
      Alert.alert('Error', 'Failed to update user');
    }
    setIsLoading(false);
  };

  return (
    <SafeAreaView style={backgroundStyle}>
      <ImageBackground source={BackgroundGradient} style={{ ...backgroundStyle, height: Dimensions.get('window').height }}>
        <Header title="Profile" navigation={navigation} hasBack />
        <View style={styles.container}>
          <View style={profileForm}>
            <KeyboardAwareScrollViewCustom isEnableScroll>
              <View
                style={{
                  flexDirection: 'column',
                  gap: 20,
                  padding: 20,
                  paddingTop: 30,
                  marginBottom: 70,
                  minHeight: Dimensions.get('window').height - 205,
                }}
              >
                <View style={titleRow}>
                  <TouchableOpacity onPress={openPicker} style={uploadButton}>
                    {file ? (
                      <View style={uploadedFileContainer}>
                        {renderThumb(file)}
                        <TouchableOpacity
                          style={trashButton}
                          onPress={() => {
                            setFile(null);
                          }}
                        >
                          <FontAwesome5 name="times" size={12} color="rgba(0,0,0,0.9)" />
                        </TouchableOpacity>
                      </View>
                    ) : (
                      <Image source={isTrade ? TradesProfileIcon : ProfileIcon} style={{ width: 50, height: 50 }} resizeMode="contain" />
                    )}
                  </TouchableOpacity>
                  <View
                    style={{
                      flex: 1,
                      height: 60,
                      flexDirection: 'column',
                      marginLeft: 10,
                    }}
                  >
                    <Text numberOfLines={1} ellipsizeMode="tail" style={formTitle}>
                      {user.user_name}
                    </Text>
                    <View style={{ width: 170, flexDirection: 'row', alignItems: 'center', gap: 5 }}>
                      <StarRating
                        disabled={true}
                        fullStarColor="#FFD700"
                        maxStars={5}
                        sty
                        rating={user.rating ? user.rating.length : 0}
                        starSize={20}
                      />
                      <Text>({user.rating ? user.rating.length : 0})</Text>
                    </View>
                  </View>
                </View>
                {isTrade ? (
                  <>
                    <View>
                      <CustomText style={title} text="Bio" />
                      <TextInput
                        style={{ ...styleInput, height: 150, borderRadius: 5 }}
                        value={bio}
                        onChangeText={setBio}
                        multiline={true}
                        placeholder="Tell us a bit about yourself. How long have you been in the trade for? Where have you worked historically? What is your expected hourly rate?"
                        placeholderTextColor="#666666"
                      />
                    </View>

                    <View>
                      <CustomText style={title} text="Documentation" />
                      <TextInput
                        style={{ ...styleInput, height: 150, borderRadius: 5 }}
                        value={documentation}
                        onChangeText={setDocumentation}
                        multiline={true}
                      />
                    </View>

                    <View>
                      <CustomText style={title} text="Qualifications" />
                      <TextInput
                        style={{ ...styleInput, height: 150, borderRadius: 5 }}
                        value={qualifications}
                        onChangeText={setQualifications}
                        multiline={true}
                      />
                    </View>

                    <View>
                      <CustomText style={title} text="Location" />
                      <View style={{ flexDirection: 'row', gap: 10 }}>
                        <View style={{ flex: 1 }}>
                          <CustomText text="City" />
                          <TextInput style={styleInput} value={city} onChangeText={setCity} />
                        </View>
                        <View style={{ flex: 1 }}>
                          <CustomText text="County" />
                          <TextInput style={styleInput} value={county} onChangeText={setCounty} />
                        </View>
                      </View>
                      <View style={{ marginTop: 10 }}>
                        <CustomText text="Travel Distance (miles)" />
                        <TextInput style={styleInput} value={travelDistance} onChangeText={setTravelDistance} keyboardType="numeric" />
                      </View>
                    </View>

                    <View>
                      <CustomText text="Documents" />
                      <FilePicker files={documentFiles} onChange={setDocumentFiles} typePicker={[DocumentPicker.types.allFiles]} />
                    </View>
                  </>
                ) : (
                  <>
                    <View>
                      <CustomText style={title} text="Contact Details: " />
                      <TextInput
                        style={{ ...styleInput, height: 80, borderRadius: 5 }}
                        value={companyContact}
                        onChangeText={setCompanyContact}
                        multiline={true}
                      />
                    </View>
                    <View>
                      <CustomText style={title} text="Tell us a bit about your company.  What do you specialise in?  What are your company values?" />
                      <TextInput
                        style={{ ...styleInput, height: 150, borderRadius: 5 }}
                        value={bio}
                        onChangeText={setBio}
                        multiline={true}
                        placeholder="Company Bio"
                        placeholderTextColor="#666666"
                      />
                    </View>
                    <View>
                      <CustomText style={title} text="Company Address: " />
                      <TextInput
                        style={{ ...styleInput, height: 80, borderRadius: 5 }}
                        value={companyAddress1}
                        onChangeText={setCompanyAddress1}
                        multiline={true}
                      />
                    </View>
                    <View>
                      <CustomText style={title} text="Order Number | Reference: " />
                      <TextInput style={styleInput} value={orderNumber} onChangeText={setOrderNumber} />
                    </View>
                    <View>
                      <CustomText style={title} text="Contact Name for Order: " />
                      <TextInput style={styleInput} value={orderContactName} onChangeText={setOrderContactName} />
                    </View>
                    <View>
                      <CustomText style={title} text="Contact Email for Invoicing: " />
                      <TextInput style={styleInput} value={invoicingContactEmail} onChangeText={setInvoicingContactEmail} />
                    </View>
                  </>
                )}
              </View>
            </KeyboardAwareScrollViewCustom>
            <View style={editButtonStyle}>
              <CustomButton isLoading={isLoading} style={{ borderRadius: 25 }} title={'Save'} color={'black'} onPress={handleProfileEditButton} />
            </View>
          </View>
        </View>
      </ImageBackground>
    </SafeAreaView>
  );
}
