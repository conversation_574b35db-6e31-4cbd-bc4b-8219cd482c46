import React, { useEffect } from 'react';
import DocumentPicker from 'react-native-document-picker';
import { SafeAreaView } from 'react-native-safe-area-context';
import { View, Text, Alert, ViewStyle, StyleSheet, TextInput, Dimensions } from 'react-native';
import Header from './Header';
import { Controller, useForm } from 'react-hook-form';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import CustomButton from './CustomButton';
import CustomText from './CustomText';
import CustomTextInput from './CustomTextInput';
import MultiSelectComponent from './elements/MultiSelect';
import Checkbox from './elements/Checkbox';
import FilePicker from './elements/FilePicker';
import { useAuth } from '../../context/AuthContext';
import KeyboardAwareScrollViewCustom from './elements/KeyboardAwareScrollViewCustom';
import FAIcon from 'react-native-vector-icons/FontAwesome';
import { handleUploadFile } from '../utils/handleuploadFile';

const baseStyles = require('../../style/style');

const tradeOptiosn = [
  'Bricklaying',
  'Carpentry',
  'Plumbing',
  'Electrical',
  'Roofing',
  'Plastering',
  'Painting',
  'Tiling',
  'Scaffolding',
  'Joinery',
].map((v) => {
  return { label: v, value: v };
});

const passwordErrorInitialState = {
  hasAtLeast8Characters: {
    value: false,
    label: 'At least 8 characters',
  },
  below20Characters: {
    value: true,
    label: 'Below 20 characters',
  },
  containsLowercase: {
    value: false,
    label: 'Contains lowercase letter',
  },
  containsUppercase: {
    value: false,
    label: 'Contains uppercase letter',
  },
  containsSpecialCharacter: {
    value: false,
    label: 'Contains special character',
  },
};

export default function Register({ navigation, route }) {
  const { onRegister } = useAuth();
  const { getValues, register, control, setValue, reset, setFocus } = useForm();
  const [isBusiness, setIsBusiness] = React.useState(false);
  const [qualificationFiles, setQualificationFiles] = React.useState([]);
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const [invoiceDetail, setInvoiceDetail] = React.useState({
    reference: '',
    contactName: '',
    contactEmail: '',
  });
  const [invoiceDetailFile, setInvoiceDetailFile] = React.useState([]);

  const [passwordError, setPasswordError] = React.useState({ ...passwordErrorInitialState });
  useEffect(() => {
    if (route.params) {
      setInvoiceDetail({ reference: route.params.reference, contactName: route.params.contactName, contactEmail: route.params.contactEmail });
      setInvoiceDetailFile(route.params.file);
    }
  }, [route]);

  const backgroundStyle = {
    backgroundColor: Colors.black,
    width: '100%',
    height: '100%',
  };

  const formContainer = {
    backgroundColor: Colors.white,
    marginTop: 16,
    borderRadius: 50,
    padding: 20,
    flexDirection: 'column',
    gap: 10,
  };

  const formContent = {
    gap: 10,
  };

  const registerRow = {
    flexDirection: 'row',
    marginTop: 10,
  };

  const registerItem = {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 40,
  };

  const passwordErrorListItem: ViewStyle = {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
    columnGap: 10,
  };

  const secondaryButton = {
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#000',
  };

  const validatePassword = (input: string) => {
    let tempPasswordError = { ...passwordError };

    tempPasswordError.hasAtLeast8Characters.value = input.length >= 8;
    tempPasswordError.below20Characters.value = input.length <= 20;
    tempPasswordError.containsLowercase.value = /[a-z]/.test(input);
    tempPasswordError.containsUppercase.value = /[A-Z]/.test(input);
    tempPasswordError.containsSpecialCharacter.value = /[!@#$%^&*(),.?":{}|<>]/.test(input);

    setPasswordError(tempPasswordError);
  };

  const CheckIcon = <FAIcon name="check-circle" size={25} color="#2bbd66" />;
  const CrossIcon = <FAIcon name="times-circle" size={25} color="#bd2b2b" />;
  const WarningIcon = <FAIcon name="warning" size={25} color="#4c3f11" />;

  const renderPasswordRequirement = () => {
    return (
      <View>
        <CustomText text="The password requires:" />

        {Object.values(passwordError).map((errorObj) => {
          return (
            <View key={errorObj.label} style={passwordErrorListItem}>
              {errorObj.value ? CheckIcon : CrossIcon}
              <CustomText text={errorObj.label} isBold />
            </View>
          );
        })}
      </View>
    );
  };

  const renderUserType = () => {
    return (
      <>
        <CustomText text="Are you registering for:" />
        <View style={{ ...registerRow, flexDirection: 'row' }}>
          <View style={registerItem as ViewStyle}>
            <Checkbox
              isChecked={!isBusiness}
              onPress={() => {
                reset();
                setFocus('name');
                setIsBusiness(false);
                setPasswordError(passwordErrorInitialState);
              }}
            />
            <CustomText text="Trade" />
          </View>
          <View style={registerItem as ViewStyle}>
            <Checkbox
              isChecked={isBusiness}
              onPress={() => {
                reset();
                setFocus('name');
                setIsBusiness(true);
                setPasswordError(passwordErrorInitialState);
              }}
            />
            <CustomText text="Business" />
          </View>
        </View>
      </>
    );
  };

  const renderInvoiceDetailButton = () => {
    const hasFilled = invoiceDetail.reference && invoiceDetail.contactName && invoiceDetail.contactEmail;
    const filledFieldNumber = Object.values(invoiceDetail).filter((val) => val).length;
    const partialFilled = filledFieldNumber >= 1 && filledFieldNumber < 3;

    let _iconComponent = undefined;
    if (hasFilled) _iconComponent = CheckIcon;
    if (partialFilled) _iconComponent = WarningIcon;

    return (
      <CustomButton
        style={secondaryButton}
        navigation={navigation}
        title="Add Order Number and Invoice Details"
        textStyle={{ fontSize: 14, width: hasFilled || partialFilled || isSubmitting ? Dimensions.get('window').width - 150 : '100%' }}
        iconComponent={_iconComponent}
        // to="RegisterInvoiceDetails"
        onPress={() => navigation.navigate('RegisterInvoiceDetails', { invoiceDetail, file: invoiceDetailFile })}
        color="white"
        isLoading={isSubmitting}
      />
    );
  };

  const handleRegister = async () => {
    if (isSubmitting) return;
    setIsSubmitting(true);
    try {
      const values = getValues();

      if (!values.email || !values.password || !values.confirmPassword || !values.name) {
        Alert.alert('Please fill in all the fields');
        setIsSubmitting(false);
        return;
      }

      const re =
        /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
      if (!re.test(String(values.email).toLowerCase())) {
        Alert.alert('Please enter a valid email address');
        setIsSubmitting(false);
        return;
      }

      if (Object.values(passwordError).some((v) => !v.value)) {
        Alert.alert('Please confirm your password is following the requirements');
        setIsSubmitting(false);
        return;
      }

      if (values.password !== values.confirmPassword) {
        Alert.alert('Password and confirm password do not match');
        setIsSubmitting(false);
        return;
      }
      let res;
      if (isBusiness) {
        let documents;

        if (invoiceDetailFile.length > 0) {
          documents = await Promise.all(
            invoiceDetailFile.map(async (file) => {
              return await handleUploadFile(file);
            })
          );
        }

        res = await onRegister({
          email: values.email,
          password: values.password,
          companyAddress1: values.companyAddress1,
          name: values.name,
          postcode: values.postcode,
          companyContact: values.companyContact,
          orderNumber: invoiceDetail.reference,
          orderContactName: invoiceDetail.contactName,
          orderContactEmail: invoiceDetail.contactEmail,
          orderDocuments: documents,
          type: 'Business',
        });
      } else {
        let documents = await Promise.all(
          qualificationFiles.map(async (file) => {
            return await handleUploadFile(file);
          })
        );
        res = await onRegister({
          email: values.email,
          password: values.password,
          name: values.name,
          trade: values.trade,
          postcode: values.postcode,
          qualifications: values.qualificationDescription,
          documents,
          type: 'Tradesperson',
        });
      }
      if (res?.success) {
        navigation.navigate('Home');
      } else if (res?.error) {
        let errorMessage = res.msg;
        if (typeof res.msg === 'string' && res.msg.includes('[') && res.msg.includes(']')) {
          errorMessage = res.msg.split(']')[1].trim();
        }
        Alert.alert(errorMessage);
        setIsSubmitting(false);
      } else {
        Alert.alert('Something went wrong', res?.msg);
        setIsSubmitting(false);
      }
    } catch (err) {
      setIsSubmitting(false);
      let errorMessage = err.msg;
      if (typeof err.msg === 'string' && err.msg.includes('[') && err.msg.includes(']')) {
        errorMessage = err.msg.split(']')[1].trim();
      }
      Alert.alert(errorMessage);
    }
  };

  return (
    <SafeAreaView style={backgroundStyle as ViewStyle}>
      <KeyboardAwareScrollViewCustom isEnableScroll>
        <Header title="Registration" navigation={navigation} hasBack imageContainerStyle={stylesCustom.imageContainerStyle} />
        <View style={stylesCustom.viewContainer}>
          <View style={formContainer as ViewStyle}>
            {renderUserType()}

            {/* ------------------ */}
            {/* Trader Person Part */}
            {/* ------------------ */}
            {!isBusiness && (
              <View style={formContent} key={'tradesperson'}>
                <View>
                  <CustomText text="Name" />
                  <CustomTextInput
                    control={control}
                    register={register}
                    inputRest={{
                      returnKeyType: 'next',
                      onSubmitEditing: () => setFocus('email'),
                    }}
                    name="name"
                    autoFocus
                  />
                </View>
                <View>
                  <CustomText text="Email" />
                  <CustomTextInput
                    register={register}
                    control={control}
                    inputRest={{
                      returnKeyType: 'next',
                      onSubmitEditing: () => setFocus('password'),
                    }}
                    name="email"
                  />
                </View>
                <View>
                  <CustomText text="Password" />
                  <Controller
                    control={control}
                    name={'password'}
                    render={({ field: { onChange, value } }) => (
                      <View style={{ position: 'relative' }}>
                        <TextInput
                          {...register('password')}
                          disableFullscreenUI={isSubmitting}
                          editable={!isSubmitting}
                          selectTextOnFocus={!isSubmitting}
                          autoCapitalize="none"
                          autoFocus={true}
                          style={[styles.input]}
                          value={value}
                          keyboardType={'default'}
                          blurOnSubmit={false}
                          secureTextEntry={true}
                          returnKeyType="next"
                          onSubmitEditing={() => setFocus('confirmPassword')}
                          onChangeText={(text) => {
                            validatePassword(text);
                            onChange(text);
                          }}
                        />
                      </View>
                    )}
                  />
                </View>
                {renderPasswordRequirement()}
                <View>
                  <CustomText text="Confirm Password" />
                  <CustomTextInput
                    register={register}
                    control={control}
                    returnKeyType="next"
                    name="confirmPassword"
                    inputRest={{
                      secureTextEntry: true,
                      returnKeyType: 'next',
                      onSubmitEditing: () => setFocus('postcode'),
                    }}
                  />
                </View>
                <View>
                  <CustomText text="Trade" />
                  {/* <CustomTextInput control={control} name="trade" /> */}
                  <MultiSelectComponent
                    data={tradeOptiosn}
                    value={getValues('trade')}
                    onSelectItem={(item) => setValue('trade', item)}
                    placeholder={'Select Trades'}
                  />
                </View>
                <View>
                  <CustomText text="Home Postcode" />
                  <CustomTextInput
                    register={register}
                    inputRest={{
                      returnKeyType: 'next',
                      onSubmitEditing: () => setFocus('qualificationDescription'),
                    }}
                    control={control}
                    name="postcode"
                  />
                </View>
                <View>
                  <CustomText text="Qualifications" />
                  <Text style={{ color: 'rgba(0,0,0,0.6)', fontSize: 12 }}>
                    {`Tell us about your qualifications (CSCS/TRADE CSCS cards, public liability insurance, proof of right to work in the UK)`}
                  </Text>
                  <CustomTextInput
                    register={register}
                    control={control}
                    name="qualificationDescription"
                    inputRest={{
                      returnKeyType: 'done',
                      onSubmitEditing: () => handleRegister,
                    }}
                    multiline
                    inputStyle={stylesCustom.multilineInput}
                  />
                  <FilePicker files={qualificationFiles} onChange={setQualificationFiles} typePicker={[DocumentPicker.types.allFiles]} />
                </View>
                <CustomButton navigation={navigation} title="Register" to="" color="black" onPress={handleRegister} isLoading={isSubmitting} />
              </View>
            )}

            {/* ------------------ */}
            {/* Business Part */}
            {/* ------------------ */}
            {isBusiness && (
              <View style={formContent} key={'business'}>
                <View>
                  <CustomText text="Company Name" />
                  <CustomTextInput
                    control={control}
                    register={register}
                    inputRest={{
                      returnKeyType: 'next',
                      onSubmitEditing: () => setFocus('companyAddress1'),
                    }}
                    autoFocus
                    name="name"
                  />
                </View>
                <View>
                  <CustomText text="Company Address 1" />
                  <CustomTextInput
                    control={control}
                    inputRest={{
                      returnKeyType: 'next',
                      onSubmitEditing: () => setFocus('postcode'),
                    }}
                    name="companyAddress1"
                  />
                </View>
                <View>
                  <CustomText text="Postcode" />
                  <CustomTextInput
                    control={control}
                    register={register}
                    inputRest={{
                      returnKeyType: 'next',
                      onSubmitEditing: () => setFocus('email'),
                    }}
                    name="postcode"
                  />
                </View>
                <View>
                  <CustomText text="Company Email" />
                  <CustomTextInput
                    control={control}
                    register={register}
                    inputRest={{
                      returnKeyType: 'next',
                      onSubmitEditing: () => setFocus('companyContact'),
                    }}
                    name="email"
                  />
                </View>
                <View>
                  <CustomText text="Company Contact" />
                  <CustomTextInput
                    control={control}
                    register={register}
                    inputRest={{
                      returnKeyType: 'next',
                      onSubmitEditing: () => setFocus('password'),
                    }}
                    name="companyContact"
                  />
                </View>
                <View>
                  <CustomText text="Password" />
                  <Controller
                    control={control}
                    name={'password'}
                    render={({ field: { onChange, value } }) => (
                      <View style={{ position: 'relative' }}>
                        <TextInput
                          {...register('password')}
                          disableFullscreenUI={isSubmitting}
                          editable={!isSubmitting}
                          selectTextOnFocus={!isSubmitting}
                          autoCapitalize="none"
                          autoFocus={true}
                          style={[styles.input]}
                          value={value}
                          keyboardType={'default'}
                          blurOnSubmit={false}
                          secureTextEntry={true}
                          returnKeyType="next"
                          onSubmitEditing={() => setFocus('confirmPassword')}
                          onChangeText={(text) => {
                            validatePassword(text);
                            onChange(text);
                          }}
                        />
                      </View>
                    )}
                  />
                </View>
                {renderPasswordRequirement()}
                <View>
                  <CustomText text="Confirm Password" />
                  <CustomTextInput
                    control={control}
                    register={register}
                    name="confirmPassword"
                    inputRest={{
                      secureTextEntry: true,
                      returnKeyType: 'done',
                      onSubmitEditing: () => handleRegister,
                    }}
                  />
                </View>

                {renderInvoiceDetailButton()}

                <CustomButton
                  navigation={navigation}
                  title="Register for Approval"
                  to=""
                  color="black"
                  onPress={handleRegister}
                  isLoading={isSubmitting}
                />

                {/* <View style={paymentRow}>
                    <Pressable
                      style={[
                        button,
                        buttonWidth,
                        { backgroundColor: Colors.black },
                      ]}
                      onPress={() => {
                        navigation.navigate('Payment');
                      }}>
                      <CustomText
                        text="Set up payment method now"
                        position="center"
                        color="white"
                      />
                    </Pressable>
                    <CustomText text="OR" isUnderline={true} />
                    <Pressable
                      style={[
                        button,
                        buttonWidth,
                        {
                          backgroundColor: isSelected
                            ? Colors.green
                            : Colors.black,
                        },
                      ]}
                      onPress={() => setIsSelected(!isSelected)}>
                      <CustomText
                        text="Confirm invoice details"
                        position="center"
                        color="white"
                      />
                    </Pressable>
                  </View> */}
              </View>
            )}
            <CustomText text="Back to Login" isUnderline={true} position="center" onPress={() => navigation.navigate('Login')} />
          </View>
        </View>
      </KeyboardAwareScrollViewCustom>
    </SafeAreaView>
  );
}

const stylesCustom = StyleSheet.create({
  imageContainerStyle: {
    width: 70,
    height: 50,
  },
  viewContainer: {
    ...baseStyles.container,
    marginTop: 15,
  },
  title: {
    ...baseStyles.titleMedium,
    marginBottom: 15,
  },
  multilineInput: {
    height: 100,
    textAlignVertical: 'top',
    paddingVertical: 10,
  },
});

const styles = StyleSheet.create({
  input: {
    borderWidth: 1,
    borderColor: Colors.gray,
    marginTop: 10,
    height: 40,
    paddingHorizontal: 10,
    fontFamily: 'Montserrat-Regular',
  },
});
