import React, { useEffect } from 'react';
import { ScrollView } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import { StyleSheet, View, Text, Image, Dimensions, Share, TouchableOpacity, ActivityIndicator, Linking, ImageBackground } from 'react-native';
import Header from './Header';
import CustomText from './CustomText';
import { BackgroundGradient, Forward } from '../assets/image';
import { getFlash, getFlashReadStatus } from '../services/flashService';
import { utils } from '../utils';
import UnreadRedDot from './elements/UnreadRedDot';

const backgroundStyle = {
  backgroundColor: Colors.black,
  width: '100%',
  height: '100%',
};

const newsForm = {
  backgroundColor: Colors.white,
  marginTop: 0,
  borderRadius: 25,
  flexDirection: 'column',
  gap: 20,
  height: Dimensions.get('window').height - 200,
};

const formRow = {
  borderBottomColor: Colors.gray,
  borderBottomWidth: 1,
  marginBottom: 15,
  paddingBottom: 15,
  gap: 5,
};

const forwardRow = {
  flexDirection: 'row',
  justifyContent: 'flex-end',
  alignItems: 'center',
};

const imageStyle = {
  width: 15,
  height: 15,
  marginLeft: 5,
  resizeMode: 'contain',
};

const redDotPositionStyle = { position: 'absolute', top: 5, right: 5 };

export default function Newsflash({ route, navigation }) {
  const [newsFlash, setNewsFlash] = React.useState([]);
  const [readFlash, setReadFlash] = React.useState([]);
  const styles = require('../../style/style');
  const [isLoading, setIsLoading] = React.useState(true);
  const getNews = async () => {
    setIsLoading(true);
    try {
      const response = await getFlash();
      if (response) setNewsFlash(response);
      setIsLoading(false);
    } catch (error) {
      utils.handleErrorNotify(error);
      setIsLoading(false);
    }
  };

  const getReadFlash = async () => {
    setIsLoading(true);
    try {
      const response = await getFlashReadStatus();
      if (response) setReadFlash(response);
      setIsLoading(false);
    } catch (error) {
      utils.handleErrorNotify(error);
      setIsLoading(false);
    }
  };

  React.useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      // The screen is focused
      // Call any action
      getNews();
      getReadFlash();
    });

    // Return the function to unsubscribe from the event so it gets removed on unmount
    return unsubscribe;
  }, [navigation]);

  return (
    <SafeAreaView style={backgroundStyle}>
      {/* <ScrollView> */}
      <ImageBackground source={BackgroundGradient} style={{ ...backgroundStyle, height: Dimensions.get('window').height }}>
        <Header title="Newsflash" navigation={navigation} hasBack />
        <View style={styles.container}>
          {isLoading ? (
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
              <ActivityIndicator size="large" color={Colors.white} />
            </View>
          ) : (
            <>
              <View style={newsForm}>
                <ScrollView>
                  <View
                    style={{
                      padding: 20,
                    }}
                  >
                    {newsFlash.length > 0 ? (
                      newsFlash.map((news) => (
                        <TouchableOpacity
                          key={news.id}
                          onPress={() => {
                            navigation.navigate('NewsflashDetail', { newsFlashData: news });
                          }}
                        >
                          <View style={formRow}>
                            <CustomText text={news?.poster[0]?.user_name} isBold={true} />
                            <CustomText text={news?.title_flash} />
                            <TouchableOpacity
                              onPress={() => {
                                Share.share({
                                  message: news?.flash_content,
                                  title: news?.title_flash,
                                });
                              }}
                            >
                              <View style={forwardRow}>
                                <CustomText text="Forward" isUnderline={true} />
                                <Image source={Forward} style={imageStyle} />
                              </View>
                            </TouchableOpacity>
                          </View>
                        </TouchableOpacity>
                      ))
                    ) : (
                      <CustomText style={{ textAlign: 'center', fontSize: 22 }} text={'You have no news here'} color="white" />
                    )}
                  </View>
                </ScrollView>
              </View>
            </>
          )}
        </View>
      </ImageBackground>

      {/* </ScrollView> */}
    </SafeAreaView>
  );
}
