/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import React, {useEffect} from 'react';
import {
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  View,
  Button,
  Pressable,
  Image,
} from 'react-native';

import {Colors} from 'react-native/Libraries/NewAppScreen';

import {Logo} from '../assets/image';
import {Dimensions} from 'react-native';
import CustomText from './CustomText';
import {useAuth} from '../../context/AuthContext';

function Index({navigation}): JSX.Element {
  const styles = require('../../style/style');
  const {authState} = useAuth();

  useEffect(() => {
    if (authState.authenticated) {
      console.log('authState.user', authState.user);
      navigation.navigate('Home');
    }
  }, [authState]);

  const textStyle = {
    color: Colors.white,
    textDecorationLine: 'underline',
    textAlign: 'center',
    fontFamily: 'Montserrat-Regular',
  };

  const backgroundStyle = {
    backgroundColor: Colors.black,
    width: '100%',
    height: '100%',
  };

  const win = Dimensions.get('window');

  const ratio = (win.width - 32) / 744; //744 is actual image width

  const imageStyle = {
    width: win.width - 32,
    height: 702 * ratio, //702 is actual image height
  };

  const row = {
    marginTop: 32,
    marginBottom: 32,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  };
  const button = {
    backgroundColor: Colors.white,
    borderRadius: 5,
    paddingVertical: 30,
  };
  const buttonWidth = {
    width: (Dimensions.get('window').width - 32 - 40) / 2,
  };

  return (
    <SafeAreaView style={backgroundStyle}>
      <ScrollView>
        <View style={styles.container}>
          <Image source={Logo} style={imageStyle}></Image>
          <View style={styles.sectionDescription}>
            <CustomText
              text="Login"
              position="center"
              color="white"
              isUnderline={true}
            />
            <View style={row}>
              <Pressable
                style={[button, buttonWidth]}
                onPress={() => {
                  navigation.navigate('Login', {isTrade: true});
                }}>
                <CustomText text="Trade" position="center" />
              </Pressable>
              <CustomText text="OR" color="white" isUnderline={true} />
              <Pressable
                style={[button, buttonWidth]}
                onPress={() => {
                  navigation.navigate('Login', {isTrade: false});
                }}>
                <CustomText text="Business" position="center" />
              </Pressable>
            </View>
            <Text
              style={textStyle}
              onPress={() => navigation.navigate('Register')}>
              New to trade motion?{'\n'}Register now
            </Text>
            <Text
              style={[textStyle, {marginTop: 12}]}
              onPress={() => navigation.navigate('TestMap')}>
              Test map
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  sectionDescription: {
    marginTop: 32,
  },
});

export default Index;
