import React, { useCallback, useEffect } from 'react';

import { SafeAreaView } from 'react-native-safe-area-context';
import Header from './Header';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import { ImageBackground, View } from 'react-native';
import MapViewJobs from './elements/MapViewJobs';
import { BackgroundGradient } from '../assets/image';

export default function FindJob({ route, navigation }) {
  const { job } = route.params;
  const backgroundStyle = {
    backgroundColor: Colors.black,
    width: '100%',
    height: '100%',
  };

  const jobContainer = {
    flexDirection: 'column',
    gap: 10,
    flex: 1,
  };
  return (
    <SafeAreaView style={backgroundStyle}>
      <ImageBackground source={BackgroundGradient} style={{ flex: 1 }}>
        <Header title="Job Map" navigation={navigation} hasBack={route.params?.isNoti ? false : true} />
        <View style={{ flex: 1, paddingBottom: 40, marginTop: 20 }}>
          <View style={jobContainer}>
            <View style={{ flex: 1 }}>
              <View
                style={{
                  backgroundColor: Colors.white,
                  flex: 1,
                }}
              >
                <MapViewJobs list={[job]} onJobPress={() => {}} isFullAddress />
              </View>
            </View>
          </View>
        </View>
      </ImageBackground>
    </SafeAreaView>
  );
}
