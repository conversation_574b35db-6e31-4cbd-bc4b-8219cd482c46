import React from 'react';

import { ScrollView } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StyleSheet, View, Text, Pressable, Dimensions, ImageBackground } from 'react-native';
import Header from './Header';

import { Colors } from 'react-native/Libraries/NewAppScreen';
import CustomText from './CustomText';

import BouncyCheckbox from 'react-native-bouncy-checkbox';
import CustomTextInput from './CustomTextInput';
import CustomButton from './CustomButton';
import { BackgroundGradient } from '../assets/image';

export default function Payment({ navigation }) {
  const styles = require('../../style/style');

  const backgroundStyle = {
    backgroundColor: Colors.black,
    width: '100%',
    height: '100%',
  };

  const paymentContainer = {
    backgroundColor: Colors.white,
    marginTop: 16,
    borderRadius: 25,
    padding: 20,
    flexDirection: 'column',
    gap: 10,
  };

  const row = {
    flexDirection: 'row',
  };
  return (
    <SafeAreaView style={backgroundStyle}>
      <ImageBackground source={BackgroundGradient} style={{ ...backgroundStyle, height: Dimensions.get('window').height }}>
        <ScrollView>
          <Header title="Payment method" navigation={navigation} />
          <View style={styles.container}>
            <View style={paymentContainer}>
              <View style={row}>
                <BouncyCheckbox
                  size={20}
                  fillColor={Colors.black}
                  unfillColor={Colors.white}
                  iconStyle={{ borderColor: Colors.gray }}
                  innerIconStyle={{
                    borderWidth: 1,
                    borderColor: Colors.black,
                  }}
                  isChecked={true}
                  onPress={(isChecked) => {}}
                />
                <CustomText text="Card Payment" />
              </View>
              <View>
                <CustomText text="Card Holder Name" />
                <CustomTextInput />
              </View>
              <View>
                <CustomText text="Card number" />
                <CustomTextInput />
              </View>
              <View>
                <CustomText text="Exp date" />
                <CustomTextInput half={true} />
              </View>
              <View>
                <CustomText text="CVV" />
                <CustomTextInput half={true} />
              </View>
              <View style={row}>
                <BouncyCheckbox
                  size={20}
                  fillColor={Colors.black}
                  unfillColor={Colors.white}
                  iconStyle={{ borderColor: Colors.gray }}
                  innerIconStyle={{
                    borderWidth: 1,
                    borderColor: Colors.black,
                  }}
                  isChecked={false}
                  onPress={(isChecked) => {}}
                />
                <CustomText text="Paypal" />
              </View>
              <CustomButton navigation={navigation} title="Save" color="black" to="" trade={undefined} />
            </View>
          </View>
        </ScrollView>
      </ImageBackground>
    </SafeAreaView>
  );
}
