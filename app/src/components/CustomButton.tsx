import React from 'react';

import { TouchableOpacity, ActivityIndicator } from 'react-native';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import { Text } from 'react-native';

interface CustomButtonProps {
  navigation?: any;
  title: string;
  iconComponent?: any;
  color?: string;
  to?: string;
  onPress?: () => void;
  isLoading?: boolean;
  style?: any;
  disabled?: boolean;
  backgroundColor?: string;
  textStyle?: any;
}
export default function CustomButton({
  navigation,
  title,
  color,
  to,
  onPress,
  isLoading,
  style,
  iconComponent = <></>,
  disabled = false,
  textStyle,
  backgroundColor,
}: CustomButtonProps) {
  return (
    <TouchableOpacity
      disabled={isLoading || disabled}
      style={[
        {
          paddingVertical: 10,
          flexDirection: 'row',
          borderRadius: 50,
          columnGap: 10,
          width: '100%',
          backgroundColor: backgroundColor || (color === 'black' ? Colors.black : Colors.white),
          justifyContent: 'center',
          alignItems: 'center',
        },
        style,
      ]}
      onPress={() => {
        if (onPress) {
          onPress();
        } else if (to) {
          navigation.navigate(to);
        }
      }}
    >
      {isLoading && <ActivityIndicator size="small" color={color === 'black' ? 'white' : 'black'} />}
      {iconComponent || <></>}
      <Text
        style={{
          fontFamily: 'Montserrat-Regular',
          textAlign: 'center',
          color: color === 'black' ? Colors.white : Colors.black,
          ...textStyle,
        }}
      >
        {title}
      </Text>
    </TouchableOpacity>
  );
}
