import React from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import Header from './Header';
import {Colors} from 'react-native/Libraries/NewAppScreen';
import {Text, View} from 'react-native';
import PageTitle from './PageTitle';

export default function ComingSoon({route, navigation}) {
  const styles = require('../../style/style');

  const backgroundStyle = {
    backgroundColor: Colors.black,
    width: '100%',
    height: '100%',
  };

  return (
    <SafeAreaView style={backgroundStyle}>
      {/* <ScrollView> */}
      <Header hasBack navigation={navigation} title="ComingSoon" />
      <View style={styles.container}>
        <Text
          style={{
            marginTop: 50,
            color: Colors.white,
            fontSize: 30,
          }}>
          Coming Soon
        </Text>
      </View>
      {/* </ScrollView> */}
    </SafeAreaView>
  );
}
