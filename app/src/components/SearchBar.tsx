import React from 'react';
import {Colors} from 'react-native/Libraries/NewAppScreen';
import {Dimensions, View} from 'react-native';
import {SelectList} from 'react-native-dropdown-select-list';

export default function SearchBar() {
  const data = [
    {key: '1', value: 'Trade'},
    {key: '2', value: 'Business'},
  ];

  const radius = [
    {key: '1', value: '0.5'},
    {key: '2', value: '1'},
    {key: '3', value: '1.5'},
    {key: '4', value: '2'},
  ];

  const twoColumn = {
    flexDirection: 'row',
    justifyContent: 'space-between',
  };

  const dropdownStyles = {
    backgroundColor: Colors.white,
  };

  const boxStyles = {
    backgroundColor: Colors.white,
    width: (Dimensions.get('window').width - 32 - 10) / 2,
  };
  return (
    <View style={twoColumn}>
      <SelectList
        dropdownStyles={dropdownStyles}
        boxStyles={boxStyles}
        //   setSelected={val => setSelected(val)}
        data={data}
        save="value"
        placeholder="Trade"
      />
      <SelectList
        dropdownStyles={dropdownStyles}
        boxStyles={boxStyles}
        //   setSelected={val => setSelected(val)}
        data={radius}
        save="value"
        placeholder="Search Radius"
      />
    </View>
  );
}
