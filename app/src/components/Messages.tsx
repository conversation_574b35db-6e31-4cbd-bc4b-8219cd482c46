import React, { useEffect, useState } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import Header from './Header';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import { View, Text, Dimensions, TouchableOpacity, ActivityIndicator, ImageBackground, Alert } from 'react-native';
import CustomText from './CustomText';
import { getListConversation, deleteConversation } from '../services/messagesService';
import { useAuth } from '../../context/AuthContext';
import { getUserByEmail } from '../services/usersService';
import { fromNow, utils } from '../utils';
import _ from 'lodash';
import { ScrollView, Swipeable } from 'react-native-gesture-handler';
import { BackgroundGradient } from '../assets/image';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const backgroundStyle = {
  backgroundColor: Colors.black,
  width: '100%',
  height: '100%',
};

const messageForm = {
  backgroundColor: Colors.white,
  marginTop: 0,
  borderRadius: 25,
  padding: 20,
  flexDirection: 'column' as const,
  gap: 20,
  height: Dimensions.get('window').height - 190,
};

const formContainer = {
  paddingHorizontal: 0,
};

const formRow = {
  flexDirection: 'row' as const,
  borderBottomColor: Colors.gray,
  borderBottomWidth: 1,
  marginBottom: 15,
  paddingBottom: 25,
};

const time = {
  flexDirection: 'row' as const,
  flex: 1,
  justifyContent: 'flex-end' as const,
  alignItems: 'flex-end' as const,
  position: 'absolute' as const,
  right: 0,
  bottom: 7,
};

const deleteButtonStyle = {
  borderRadius: 8,
  borderWidth: 1,
  borderColor: 'red',
  justifyContent: 'center' as const,
  alignItems: 'center' as const,
  width: 35,
  height: 40,
};

const styles = require('../../style/style');

export default function Messages({ _route, navigation }) {
  const { authState } = useAuth();
  const userEmail = authState.user.email;
  const [users, setUsers] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  const [messages, setMessages] = useState([]);

  const getUserByEmailReceiver = async (emails) => {
    const emailReceivers = emails.filter((email) => email !== userEmail)[0];
    const userByEmail = await getUserByEmail(emailReceivers);
    return userByEmail;
  };

  const getEmailReceiver = (emails) => {
    return emails.filter((email) => email !== userEmail)[0];
  };

  const fetchMessages = async () => {
    try {
      setIsLoading(true);

      const response = await getListConversation();
      if (response) {
        setMessages(response);
      }
      const fetchedUsers = await Promise.all(
        response.map(async (message) => {
          const userData = await getUserByEmailReceiver(message?.user_emails);
          return { [userData.email]: userData.user_name };
        })
      );

      const usersObject = fetchedUsers.reduce((obj, item) => ({ ...obj, ...item }), {});
      setUsers(usersObject);
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      utils.handleErrorNotify('Fail to fetch conversation detail');
    }
  };

  const handleDeleteConversation = async (id) => {
    try {
      setIsLoading(true);
      const response = await deleteConversation({ id });
      if (response) {
        fetchMessages();
      }
    } catch (error) {
      console.log('error', error);
      utils.handleErrorNotify('Failed to delete conversation');
    } finally {
      setIsLoading(false);
    }
  };

  const confirmDelete = (id) => {
    Alert.alert('Delete Conversation', 'Are you sure you want to delete this conversation?', [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Delete', style: 'destructive', onPress: () => handleDeleteConversation(id) },
    ]);
  };

  const renderRightActions = (id) => {
    return (
      <TouchableOpacity style={deleteButtonStyle} onPress={() => confirmDelete(id)}>
        <Icon name="delete" size={28} color="red" />
      </TouchableOpacity>
    );
  };

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      fetchMessages();
    });
    return () => {
      unsubscribe();
    };
  }, []);

  useEffect(() => {
    fetchMessages();
  }, []);

  return (
    <SafeAreaView style={{ ...backgroundStyle, height: Dimensions.get('window').height as any }}>
      <ImageBackground source={BackgroundGradient} style={{ ...backgroundStyle, height: Dimensions.get('window').height as any }}>
        <Header title="Messages" navigation={navigation} hasBack />
        <View style={styles.container}>
          <View style={messageForm}>
            <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
              <View style={formContainer}>
                {isLoading ? (
                  <View style={styles.loading}>
                    <ActivityIndicator size="large" color={Colors.black} />
                  </View>
                ) : (
                  <>
                    {messages.map((message) => {
                      const userindex = message?.unique_email_id.indexOf(message?.last_send_message_user) === 0 ? 0 : 1;
                      const dataLastSeen = message[`last_seen_user_${userindex + 1}`];
                      const timeToShow = _.isEmpty(dataLastSeen) ? message.date : dataLastSeen;
                      return (
                        <Swipeable key={message.id} renderRightActions={() => renderRightActions(message.id)}>
                          <TouchableOpacity
                            onPress={() => {
                              navigation.navigate('Chat', {
                                conversationId: message.id,
                                name: users[getEmailReceiver(message?.user_emails)] || getEmailReceiver(message?.user_emails),
                                read_status: message?.read_status[0],
                              });
                            }}
                          >
                            <View style={formRow}>
                              <View style={styles.messages}>
                                <CustomText
                                  text={users[getEmailReceiver(message?.user_emails)] || getEmailReceiver(message?.user_emails)}
                                  style={{ fontSize: 18 }}
                                  isBold={true}
                                />
                                <CustomText
                                  numberOfLines={1}
                                  style={{ marginVertical: 5 }}
                                  isBold={message?.read_status[0] === 'unread' && message?.last_send_message_user !== userEmail}
                                  text={message.last_message}
                                />
                              </View>
                              <View style={time}>
                                <Text>
                                  {fromNow(timeToShow, ['MM/DD/YYYY HH:mm:ss', 'DD/MM/YYYY HH:mm:ss', 'YYYY-MM-DD', 'YYYY-MM-DDTHH:mm:ss'])}
                                </Text>
                              </View>
                            </View>
                          </TouchableOpacity>
                        </Swipeable>
                      );
                    })}
                  </>
                )}
              </View>
            </ScrollView>
          </View>
        </View>
      </ImageBackground>
    </SafeAreaView>
  );
}
