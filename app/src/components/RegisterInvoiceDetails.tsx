import React, { useEffect } from 'react';
import DocumentPicker from 'react-native-document-picker';
import { SafeAreaView } from 'react-native-safe-area-context';
import { View, ViewStyle, StyleSheet, Alert } from 'react-native';
import Header from './Header';
import { useForm } from 'react-hook-form';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import CustomButton from './CustomButton';
import CustomText from './CustomText';
import CustomTextInput from './CustomTextInput';
import FilePicker from './elements/FilePicker';
import KeyboardAwareScrollViewCustom from './elements/KeyboardAwareScrollViewCustom';

const baseStyles = require('../../style/style');

export default function RegisterInvoiceDetails({ navigation, route }) {
  const { getValues, register, control, setFocus } = useForm();
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const [invoiceDetail, setInvoiceDetail] = React.useState<{ reference: string; contactName: string; contactEmail: string }>(
    route.params.invoiceDetail
  );
  const [invoiceDetailFile, setInvoiceDetailFile] = React.useState(route.params.file || []);

  const backgroundStyle = {
    backgroundColor: Colors.black,
    width: '100%',
    height: '100%',
  };

  const formContainer = {
    backgroundColor: Colors.white,
    marginTop: 16,
    borderRadius: 50,
    padding: 20,
    flexDirection: 'column',
    gap: 10,
  };

  const formContent = {
    gap: 10,
  };

  const handleFinish = () => {
    setIsSubmitting(true);

    const re =
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if (getValues('contactEmail') && !re.test(String(getValues('contactEmail')).toLowerCase())) {
      Alert.alert('Please enter a valid email address');
      setIsSubmitting(false);
      return;
    }

    navigation.navigate('Register', {
      ...getValues(),
      file: invoiceDetailFile,
    });
  };

  const introduction = 'If you are a business or company using an order number for authorisation and invoicing please provide the details below.';

  return (
    <SafeAreaView style={backgroundStyle as ViewStyle}>
      <KeyboardAwareScrollViewCustom isEnableScroll>
        <Header title="Order Details" navigation={navigation} hasBack={false} imageContainerStyle={stylesCustom.imageContainerStyle} />
        <View style={stylesCustom.viewContainer}>
          <View style={formContainer as ViewStyle}>
            <View style={formContent} key={'business'}>
              <View>
                <CustomText text={introduction} />
              </View>
              <View>
                <CustomText text="Order Number | Reference" />
                <CustomTextInput
                  control={control}
                  defaultValue={invoiceDetail.reference}
                  register={register}
                  inputRest={{
                    returnKeyType: 'next',
                    onSubmitEditing: () => setFocus('contactName'),
                  }}
                  autoFocus
                  name="reference"
                />
              </View>
              <View>
                <CustomText text="Contact Name for Order" />
                <CustomTextInput
                  control={control}
                  defaultValue={invoiceDetail.contactName}
                  inputRest={{
                    returnKeyType: 'next',
                    onSubmitEditing: () => setFocus('contactEmail'),
                  }}
                  name="contactName"
                />
              </View>
              <View>
                <CustomText text="Contact Email for Invoicing" />
                <CustomTextInput
                  control={control}
                  defaultValue={invoiceDetail.contactEmail}
                  register={register}
                  inputRest={
                    {
                      // returnKeyType: 'next',
                      // onSubmitEditing: () => setFocus('contactEmail'),
                    }
                  }
                  name="contactEmail"
                />
              </View>

              <FilePicker
                files={invoiceDetailFile}
                onChange={setInvoiceDetailFile}
                typePicker={[DocumentPicker.types.allFiles]}
                label="Attach Photo of Order (if applicable)"
              />

              <CustomButton navigation={navigation} title="Finish and Go back" to="" color="black" onPress={handleFinish} isLoading={isSubmitting} />
            </View>

            <CustomText
              text="Cancel"
              isUnderline={true}
              position="center"
              onPress={() => {
                if (navigation.canGoBack()) {
                  navigation.goBack();
                }
              }}
            />
          </View>
        </View>
      </KeyboardAwareScrollViewCustom>
    </SafeAreaView>
  );
}

const stylesCustom = StyleSheet.create({
  imageContainerStyle: {
    width: 70,
    height: 50,
  },
  viewContainer: {
    ...baseStyles.container,
    marginTop: 15,
  },
  title: {
    ...baseStyles.titleMedium,
    marginBottom: 15,
  },
  multilineInput: {
    height: 100,
    textAlignVertical: 'top',
    paddingVertical: 10,
  },
});
