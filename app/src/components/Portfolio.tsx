import React, { useState, useEffect } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import Header from './Header';
import { View, Image, Dimensions, TouchableOpacity, Alert, ActivityIndicator, ImageBackground, StyleSheet, Modal, ScrollView } from 'react-native';
import { BackgroundGradient } from '../assets/image';
import { useAuth } from '../../context/AuthContext';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import { launchImageLibrary } from 'react-native-image-picker';
import { uploadFile } from '../services/fileUploadService';
import { getUserById } from '../services/usersService';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  backgroundStyle: {
    backgroundColor: Colors.black,
    width: '100%',
    height: '100%',
  },
  portfolioForm: {
    backgroundColor: Colors.white,
    borderRadius: 25,
    flexDirection: 'column',
    height: Dimensions.get('window').height - 205,
    padding: 20,
  },
  uploadButton: {
    padding: 5,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    columnGap: 5,
    backgroundColor: 'white',
    borderRadius: 15,
    height: 150,
    width: 150,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: 'rgba(0,0,0,1)',
    margin: 5,
  },
  uploadedImageContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 15,
    margin: 5,
    height: 150,
    width: 150,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  thumb: {
    aspectRatio: 1,
    borderRadius: 15,
    height: 150,
    width: 150,
    overflow: 'hidden',
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  trashButton: {
    position: 'absolute',
    width: 25,
    height: 25,
    borderRadius: 30,
    top: 5,
    right: 5,
    backgroundColor: '#eeeee0',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    padding: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    marginTop: 10,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.9)',
  },
  modalScrollView: {
    flex: 1,
  },
  modalImage: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height * 0.8,
    resizeMode: 'contain',
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
});

export default function Portfolio({ route, navigation }) {
  const { authState, updateUser } = useAuth();
  const isView = route.params?.isView;
  const userId = route.params?.userId;
  
  const [portfolioImages, setPortfolioImages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [isImagePreviewVisible, setIsImagePreviewVisible] = useState(false);

  useEffect(() => {
    const loadImages = async () => {
      if (isView && userId) {
        try {
          setIsLoading(true);
          const userData = await getUserById(userId);
          setPortfolioImages(userData?.portfolio_images || []);
        } catch (error) {
          console.log('Error loading portfolio images:', error);
          Alert.alert('Error', 'Failed to load portfolio images');
        } finally {
          setIsLoading(false);
        }
      } else {
        setPortfolioImages(authState.user?.portfolio_images || []);
      }
    };
    
    loadImages();
  }, [isView, userId, authState.user?.portfolio_images]);

  const openImagePicker = async () => {
    const result = await launchImageLibrary({
      mediaType: 'photo',
      quality: 1,
      selectionLimit: 1,
    });

    if (result.assets && result.assets[0]) {
      const file = {
        uri: result.assets[0].uri,
        type: result.assets[0].type,
        name: result.assets[0].fileName || 'image.jpg',
      };
      handleUploadImage(file);
    }
  };

  const handleUploadImage = async (file) => {
    try {
      setIsLoading(true);
      const formData = new FormData();
      formData.append('file', file);
      const data = await uploadFile(formData);

      if (data.link) {
        const newImages = [...portfolioImages, data.link];
        setPortfolioImages(newImages);

        const userRes = await updateUser({
          portfolio_images: newImages,
        });

        if (userRes.success) {
          Alert.alert('Success', 'Portfolio image uploaded successfully');
        }
      }
    } catch (error) {
      console.log('Upload image error', error);
      Alert.alert('Error', 'Failed to upload image');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteImage = async (imageUrl) => {
    try {
      setIsLoading(true);
      const newImages = portfolioImages.filter((img) => img !== imageUrl);
      setPortfolioImages(newImages);

      const userRes = await updateUser({
        portfolio_images: newImages,
      });

      if (userRes.success) {
        Alert.alert('Success', 'Image removed successfully');
      }
    } catch (error) {
      console.log('Delete image error', error);
      Alert.alert('Error', 'Failed to remove image');
    } finally {
      setIsLoading(false);
    }
  };

  const handleImagePress = (imageUrl) => {
    setSelectedImage(imageUrl);
    setIsImagePreviewVisible(true);
  };

  const renderImage = (imageUrl) => (
    <View key={imageUrl} style={styles.uploadedImageContainer}>
      <TouchableOpacity style={styles.thumb} onPress={() => handleImagePress(imageUrl)}>
        <Image source={{ uri: imageUrl }} style={{ height: '100%', width: '100%' }} resizeMode="cover" />
      </TouchableOpacity>
      {!isView && (
        <TouchableOpacity
          style={styles.trashButton}
          onPress={() => {
            Alert.alert('Delete Image', 'Are you sure you want to remove this image?', [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Delete', style: 'destructive', onPress: () => handleDeleteImage(imageUrl) },
            ]);
          }}
        >
          <FontAwesome5 name="times" size={15} color="rgba(0,0,0,0.9)" />
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.backgroundStyle}>
      <ImageBackground source={BackgroundGradient} style={{ ...styles.backgroundStyle, height: Dimensions.get('window').height }}>
        <Header title="Portfolio" navigation={navigation} hasBack />
        <View style={styles.container}>
          <View style={styles.portfolioForm}>
            {isLoading ? (
              <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <ActivityIndicator size="large" color={Colors.black} />
              </View>
            ) : (
              <ScrollView>
                <View style={styles.imageGrid}>
                  {!isView && (
                    <TouchableOpacity onPress={openImagePicker} style={styles.uploadButton}>
                      <FontAwesome5 name="plus" size={30} color="black" />
                    </TouchableOpacity>
                  )}
                  {portfolioImages.map(renderImage)}
                </View>
              </ScrollView>
            )}
          </View>
        </View>
      </ImageBackground>

      <Modal visible={isImagePreviewVisible} transparent animationType="fade">
        <View style={styles.modalContainer}>
          <TouchableOpacity style={styles.closeButton} onPress={() => setIsImagePreviewVisible(false)}>
            <FontAwesome5 name="times" size={20} color="black" />
          </TouchableOpacity>
          <ScrollView
            style={styles.modalScrollView}
            contentContainerStyle={{ alignItems: 'center', justifyContent: 'center', minHeight: '100%' }}
            maximumZoomScale={3}
            minimumZoomScale={1}
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
          >
            <Image source={{ uri: selectedImage }} style={styles.modalImage} />
          </ScrollView>
        </View>
      </Modal>
    </SafeAreaView>
  );
}
 