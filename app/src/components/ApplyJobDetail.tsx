import React, { useState } from 'react';

import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import { View, Text, Dimensions, Pressable, Alert, ImageBackground, Linking, Image, TouchableOpacity } from 'react-native';
import moment from 'moment';
import Modal from 'react-native-modal';
import CustomText from './CustomText';
import { useAuth } from '../../context/AuthContext';
import { applyJob, setFavoriteJob, useGetFavoriteJobList } from '../services/jobsService';
import CustomButton from './CustomButton';
import Header from './Header';
import { BackgroundGradient, ProfileIcon } from '../assets/image';

const backgroundStyle = {
  backgroundColor: Colors.black,
  width: '100%',
  height: '100%',
};

const companyContainer = {
  flexDirection: 'row' as const,
  alignItems: 'center' as const,
};

const companyLogo = {
  width: 50,
  height: 50,
  borderRadius: 25,
  marginRight: 15,
};

const companyName = {
  fontSize: 18,
  fontFamily: 'Montserrat-Bold',
  color: Colors.black,
};

const twoColumn = {
  position: 'absolute',
  bottom: 50,
  left: 0,
  right: 0,
  flexDirection: 'row',
  justifyContent: 'space-between',
  paddingHorizontal: 15,
};

const modalButton = {
  borderRadius: 15,
  backgroundColor: Colors.black,
  paddingVertical: 10,
};

const modalButtonText = {
  color: Colors.white,
  textAlign: 'center',
  fontFamily: 'Montserrat-Regular',
};

const modalButtonWidth = {
  width: (Dimensions.get('window').width - 64 - 20) / 2,
};

const modalStyle = {
  backgroundColor: Colors.white,
  borderRadius: 25,
  marginTop: 32,
  marginHorizontal: 16,
  height: Dimensions.get('window').height - 170,
};

const modalApplyStyle = {
  backgroundColor: Colors.white,
  borderRadius: 25,
  padding: 20,
  height: undefined,
  maxWidth: '90%',
};

const modalContainer = {
  marginTop: 32,
  marginBottom: 32,
  paddingHorizontal: 16,
  gap: 20,
  flexDirection: 'column',
  height: '100%',
};

const modalTable = {
  alignItems: 'center',
  justifyContent: 'center',
  gap: 20,
};

const tableRow = {
  alignSelf: 'stretch',
  flexDirection: 'column',
  width: '100%',
};

const rowTitle = {
  alignSelf: 'stretch',
};

const rowCell = {
  marginTop: 15,
  marginLeft: 15,
  alignSelf: 'stretch',
  flexDirection: 'row',
  borderBottomWidth: 1,
  borderColor: 'rgba(0,0,0,0.2)',
  paddingBottom: 5,
  alignItems: 'center',
};

export default function ApplyJobDetail({ route, navigation }) {
  const { activeJob, onJobUpdate } = route?.params;
  const { authState } = useAuth();
  const senderUser = authState.user;
  const [modalApplyVisible, setModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  let isFavorite = false;
  if (typeof activeJob.favorited_by_users === 'boolean') {
    isFavorite = activeJob.favorited_by_users;
  } else {
    isFavorite = activeJob.favorited_by_users.includes(String(senderUser.id));
  }

  const handleClickApply = async () => {
    const user = authState.user;
    try {
      setIsLoading(true);
      const res = await applyJob(activeJob.id, user, activeJob.user_post?.[0]?.id);
      if (res.success) {
        setIsSuccess(true);
        setIsLoading(false);
      }
    } catch (err) {
      Alert.alert('Error when apply: ' + String(err));
    }
  };

  const handleClickFavorite = () => {
    const jobId = activeJob.id;

    // Prepare job data to avoid backend queries
    const jobData = {
      businessOwnerEmail: activeJob.user_post?.[0]?.email,
      businessOwnerName: activeJob.user_post?.[0]?.user_name,
      jobDescription: activeJob.task || activeJob.title,
      tradeUserName: senderUser.user_name,
      tradeUserEmail: senderUser.email,
      isFavorite: isFavorite,
    };
    setIsLoading(true);
    setFavoriteJob(jobId, jobData)
      .then((response) => {
        if (response.success) {
          activeJob.favorited_by_users = response.data.isFavorite ? true : false;
        }
        if (onJobUpdate) {
          onJobUpdate();
        }

        setIsLoading(false);
      })
      .catch((error) => {
        console.error('Favorite error:', error);
        setIsLoading(false);
      });
  };

  const navigateToCompanyProfile = () => {
    navigation.navigate('Profile', { isView: true, userId: activeJob.user_post?.[0]?.ID });
  };

  return (
    <>
      <SafeAreaView style={backgroundStyle}>
        <ImageBackground source={BackgroundGradient} style={{ flex: 1 }}>
          <Header title="Job detail" navigation={navigation} hasBack={route.params?.isNoti ? false : true} />
          <View style={modalStyle}>
            <View style={modalContainer}>
              <TouchableOpacity onPress={navigateToCompanyProfile}>
                <View style={companyContainer}>
                  <Image source={activeJob.user_post?.[0]?.logo_url ? { uri: activeJob.user_post[0].logo_url } : ProfileIcon} style={companyLogo} />
                  <CustomText style={companyName} text={activeJob.user_post?.[0]?.user_name || 'Company Name'} />
                </View>
              </TouchableOpacity>
              <View style={modalTable}>
                <View style={tableRow}>
                  <View style={rowTitle}>
                    <CustomText isBold text="Task description: " />
                  </View>
                  <View style={rowCell}>
                    <CustomText text={activeJob?.task || ''} />
                  </View>
                </View>
                <View style={tableRow}>
                  <View style={rowTitle}>
                    <CustomText isBold text="Date:" />
                  </View>
                  <View style={rowCell}>
                    <CustomText text={moment.utc(activeJob?.start_date_required).local().format('DD MMM YYYY') || ''} />
                  </View>
                </View>
                <View style={tableRow}>
                  <View style={rowTitle}>
                    <CustomText isBold text="Arrival time:" />
                  </View>
                  <View style={rowCell}>
                    <CustomText text={moment.utc(activeJob?.start_date_required).local().format('hh:ssa') || ''} />
                  </View>
                </View>
                <View style={tableRow}>
                  <View style={rowTitle}>
                    <CustomText isBold text="Price: " />
                  </View>
                  <View style={rowCell}>
                    <CustomText text={activeJob?.price === 'poa' ? 'POA' : '£ ' + activeJob?.price} />
                  </View>
                </View>
              </View>
              <View>
                <CustomText isBold text="Requirements: " style={{ marginBottom: 5, fontWeight: 'bold' }} />
                {activeJob?.requirements.length > 0 &&
                  activeJob?.requirements?.map((req, index) => (
                    <View key={index} style={{ flexDirection: 'row', marginBottom: 5 }}>
                      <CustomText text={'\u2022'} />
                      <CustomText text={req} />
                    </View>
                  ))}
              </View>

              <View style={[twoColumn, { bottom: 110 }]}>
                <CustomButton
                  style={[modalButton, modalButtonWidth]}
                  isLoading={isLoading}
                  onPress={handleClickFavorite}
                  title={isFavorite ? 'Unfavourite' : 'Favourite'}
                  color="black"
                />
              </View>

              <View style={twoColumn}>
                <Pressable
                  style={[modalButton, modalButtonWidth]}
                  onPress={() => {
                    setModalVisible(!modalApplyVisible);
                  }}
                >
                  <Text style={modalButtonText}>Apply</Text>
                </Pressable>
                <Pressable
                  style={[modalButton, modalButtonWidth]}
                  onPress={() => {
                    navigation.navigate('Chat', {
                      members: [
                        { email: senderUser.email, name: senderUser.user_name },
                        { email: activeJob.user_post?.[0]?.email, name: activeJob.user_post?.[0]?.user_name },
                      ],
                      name: activeJob.user_post?.[0]?.user_name,
                    });
                  }}
                >
                  <Text style={modalButtonText}>Message</Text>
                </Pressable>
              </View>
            </View>
          </View>
        </ImageBackground>
      </SafeAreaView>
      <Modal
        style={{ justifyContent: 'center', alignItems: 'center' }}
        isVisible={modalApplyVisible}
        onBackdropPress={() => {
          setModalVisible(!modalApplyVisible);
        }}
      >
        <View style={modalApplyStyle}>
          {isSuccess ? (
            <>
              <Text style={{ fontSize: 18, padding: 10, textAlign: 'center' }}>You have successfully applied</Text>
              <CustomButton
                style={{ width: 100, marginTop: 30, borderRadius: 15, alignSelf: 'center' }}
                title="Close"
                color="black"
                onPress={() => {
                  setIsSuccess(false);
                  setModalVisible(!modalApplyVisible);
                  navigation.navigate('Applications');
                }}
              />
            </>
          ) : (
            <>
              <Text style={{ fontSize: 16, marginBottom: 20, textAlign: 'left', lineHeight: 22 }}>
                By clicking apply your application will be sent to the business / contractor for approval. Should you be successful you will be
                required to attend at the requested date and time with evidence of the specific requirements. Failure to do so will likely effect your
                payment and will effect your TradeMotion rating.
              </Text>
              <Text style={{ fontSize: 16, marginBottom: 20, textAlign: 'left', lineHeight: 22 }}>
                TradeMotion holds no responsibility for payment between yourself and the business / contractor. Please refer to our{' '}
                <Text
                  style={{ color: '#007AFF', textDecorationLine: 'underline' }}
                  onPress={() => {
                    Linking.openURL('https://trademotion.vercel.app/terms');
                  }}
                >
                  terms and conditions
                </Text>{' '}
                for further information.
              </Text>
              <View style={{ flexDirection: 'row', marginTop: 10, gap: 10, alignItems: 'center', justifyContent: 'space-around' }}>
                <CustomButton
                  style={{ width: 120, borderRadius: 15, backgroundColor: '#666666' }}
                  textStyle={{ fontWeight: 'bold', fontSize: 16, color: 'white' }}
                  title="Cancel"
                  color="white"
                  onPress={() => setModalVisible(!modalApplyVisible)}
                />
                <CustomButton
                  style={{ width: 120, borderRadius: 15, backgroundColor: '#1b53ac' }}
                  textStyle={{ fontWeight: 'bold', fontSize: 16, color: 'white' }}
                  title="Confirm"
                  color="white"
                  isLoading={isLoading}
                  onPress={() => {
                    handleClickApply();
                  }}
                />
              </View>
            </>
          )}
        </View>
      </Modal>
    </>
  );
}
