import React from 'react';
import { Controller } from 'react-hook-form';

import { StyleSheet, Text, TextInput, View } from 'react-native';
import { Colors } from 'react-native/Libraries/NewAppScreen';

export default function CustomTextInput({
  control,
  name,
  rules,
  defaultValue,
  register = () => {},
  inputRest = {},
  inputStyle = {},
  multiline = false,
  isLoading = false,
  autoFocus = false,
  isNumber = false,
  isDisabled = false,
  suffix = null,
  prefix = null,
}: any) {
  return (
    <Controller
      control={control}
      name={name}
      rules={rules}
      defaultValue={defaultValue}
      render={({ field: { onChange, value }, fieldState: { error } }) => (
        <View style={{ position: 'relative' }}>
          <TextInput
            {...register(name)}
            disableFullscreenUI={isLoading}
            editable={!isLoading && !isDisabled}
            selectTextOnFocus={!isLoading && !isDisabled}
            autoCapitalize="none"
            autoFocus={autoFocus}
            style={[styles.input, inputStyle, isDisabled && { backgroundColor: Colors.lighter }]}
            value={value}
            keyboardType={isNumber ? 'numeric' : 'default'}
            blurOnSubmit={false}
            onChangeText={(text) => {
              if (isNumber) {
                const filteredText = text.replace(/[^0-9.]/g, '');
                onChange(filteredText);
              } else {
                onChange(text);
              }
            }}
            multiline={multiline}
            {...inputRest}
          />
          {prefix && <View style={{ position: 'absolute', top: 0, left: 10 }}>{prefix}</View>}
          {suffix && <View style={{ position: 'absolute', top: 0, right: 10 }}>{suffix}</View>}
          {error && <Text style={styles.errors}>{error.message}</Text>}
        </View>
      )}
    />
  );
}

const styles = StyleSheet.create({
  input: {
    borderWidth: 1,
    borderColor: Colors.gray,
    marginTop: 10,
    height: 40,
    paddingHorizontal: 10,
    fontFamily: 'Montserrat-Regular',
  },
  errors: {
    color: 'red',
    marginTop: 4,
  },
});
