import React, { useState } from 'react';
import { Modal, View, Text, TouchableOpacity, Alert, StyleSheet, Dimensions, ActivityIndicator } from 'react-native';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import { useForm } from 'react-hook-form';
import CustomButton from './CustomButton';
import CustomTextInput from './CustomTextInput';
import CustomText from './CustomText';
import { useAuth } from '../../context/AuthContext';
import { utils } from '../utils';

interface ForgotPasswordModalProps {
  visible: boolean;
  onClose: () => void;
}

export default function ForgotPasswordModal({ visible, onClose }: ForgotPasswordModalProps) {
  const { onForgotPassword } = useAuth();
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm();
  const [isLoading, setIsLoading] = useState(false);

  const onSubmit = async (data: { email: string }) => {
    setIsLoading(true);
    try {
      const result = await onForgotPassword(data.email);

      if (result?.success) {
        Alert.alert('Email Sent', result.msg, [
          {
            text: 'OK',
            onPress: () => {
              reset();
              onClose();
            },
          },
        ]);
      } else {
        utils.handleErrorNotify(result?.msg || 'Failed to send reset email');
      }
    } catch (error) {
      utils.handleErrorNotify(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Modal visible={visible} transparent={true} animationType="slide" onRequestClose={handleClose}>
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <View style={styles.header}>
            <CustomText text="Forgot Password" isBold style={styles.title} />
            <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
              <Text style={styles.closeButtonText}>×</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.content}>
            <CustomText text="Enter your email address and we'll send you a link to reset your password." style={styles.description} />

            <CustomTextInput
              control={control}
              name="email"
              defaultValue=""
              rules={{
                required: 'Email is required',
                pattern: {
                  value: /^\S+@\S+$/i,
                  message: 'Please enter a valid email address',
                },
              }}
              inputRest={{
                placeholder: 'Enter your email',
                keyboardType: 'email-address',
                autoCapitalize: 'none',
                autoCorrect: false,
              }}
              inputStyle={styles.input}
            />

            <View style={styles.buttonContainer}>
              <CustomButton
                title={isLoading ? 'Sending...' : 'Send Reset Email'}
                color="black"
                onPress={handleSubmit(onSubmit)}
                isLoading={isLoading}
                disabled={isLoading}
                style={styles.submitButton}
              />

              <CustomButton
                title="Cancel"
                color="white"
                onPress={handleClose}
                style={[styles.cancelButton, { borderColor: Colors.black, borderWidth: 1 }]}
                disabled={isLoading}
              />
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: Colors.white,
    borderRadius: 20,
    padding: 0,
    width: width * 0.9,
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.lighter,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Montserrat-Bold',
  },
  closeButton: {
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 24,
    color: Colors.black,
    fontWeight: 'bold',
  },
  content: {
    padding: 20,
  },
  description: {
    marginBottom: 20,
    textAlign: 'center',
    color: Colors.dark,
    lineHeight: 20,
  },
  input: {
    marginTop: 10,
    marginBottom: 10,
  },
  buttonContainer: {
    marginTop: 20,
    gap: 12,
  },
  submitButton: {
    paddingVertical: 12,
  },
  cancelButton: {
    paddingVertical: 12,
  },
});
