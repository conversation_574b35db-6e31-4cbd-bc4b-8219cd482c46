import React from 'react';
import { Platform } from 'react-native';
import CustomButton from './CustomButton';
import moment from 'moment';

interface DateTimePickerProviderProps {
  children: React.ReactNode;
  label?: string;
  androidOpenModal: any;
}

export default function DateTimePickerProvider({ children, label, androidOpenModal }: DateTimePickerProviderProps) {
  const [show, setShow] = React.useState(false);

  const os = Platform.OS;

  return (
    <>
      {os === 'android' && (
        <CustomButton
          style={{ height: 35, width: 160, alignItems: 'center', justifyContent: 'center', backgroundColor: 'gray' }}
          title={label}
          onPress={() => {
            androidOpenModal();
            setShow(true);
          }}
          color="white"
        />
      )}
      {(os === 'ios' || show) && children}
    </>
  );
}
