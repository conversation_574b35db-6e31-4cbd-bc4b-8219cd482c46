export const getTradeOptions = (args1?: { excluded?: string[] }) => {
  const trades = [
    'Bricklayer',
    'Cleaner',
    'Electrician',
    'General Labourer',
    'Ground Worker',
    'Jo<PERSON>',
    '<PERSON> Fitter',
    '<PERSON>',
    'Plant Operator',
    'Plasterer',
    'Plumber',
    'Roofer',
    'Scaffolder',
    'Sealant Contractor',
    'Utilities Engineer',
    'Wall Tiler',
  ];

  return ['All', ...trades, 'Other']
    .filter((item) => {
      if (!args1 || !args1.excluded) return true;
      return !args1.excluded.includes(item);
    })
    .map((v) => {
      return { label: v, title: v, value: v };
    });
};
