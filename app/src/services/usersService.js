import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_URL } from '../../context/AuthContext';
const TOKEN_KEY = 'my-jwt';
import axiosInstance from '../utils/axiosManager';

export async function getUserByEmail(email) {
  try {
    const url = `${API_URL}/api/users`;
    const token = await AsyncStorage.getItem(TOKEN_KEY);
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    const response = await axios.request({
      url: url,
      method: 'get',
      params: {
        email: email,
      },
    });
    if (response.data.success) {
      return response.data.data[0];
    }
  } catch (error) {
    console.error('Error postData', error);
  }
}

export async function getUserById(userId) {
  try {
    const url = `${API_URL}/api/users/${userId}`;
    const token = await AsyncStorage.getItem(TOKEN_KEY);
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    const response = await axios.request({
      url: url,
      method: 'get',
    });
    if (response.code === 403) {
      return { error: 'Forbidden', code: 403 };
    }
    if (response.data.success) {
      return response.data.data;
    }
  } catch (error) {
    console.error('Error postData', error);
  }
}

export async function checkExitOrderNumber(orderNumber) {
  const url = '/api/orderNumber/checkExitOrderNumber';
  const response = await axiosInstance.request({
    url: url,
    method: 'get',
    params: {
      order_number: encodeURIComponent(orderNumber),
    },
  });
  return response.data;
}
