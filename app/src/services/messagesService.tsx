import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_URL } from '../../context/AuthContext';

const TOKEN_KEY = 'my-jwt';

type TSendMessagePayload = {
  conversation_id: string;
  unique_email_id: string;
  message: string;
  receiver_email: string;
};

type TGetConversationDetailPayload =
  | {
      id: number;
    }
  | {
      members: Array<{
        email: string;
        name: string;
      }>;
    };

export async function getListConversation() {
  try {
    const url = `${API_URL}/api/messages/getListConversation`;
    const token = await AsyncStorage.getItem(TOKEN_KEY);
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    const response = await axios.request({
      url: url,
      method: 'get',
    });
    if (response.data.success) {
      return response?.data?.data;
    }
  } catch (error) {
    console.error('Error getListConversation', error);
  }
}

export async function getConversationDetail(body: TGetConversationDetailPayload) {
  try {
    const url = `${API_URL}/api/messages/getConversationDetail`;
    const token = await AsyncStorage.getItem(TOKEN_KEY);
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    const response = await axios.request({
      url: url,
      data: body,
      method: 'post',
    });
    if (response.data.success) {
      return response?.data?.data;
    }
  } catch (error) {
    console.error('Error getConversationDetail', error);
  }
}

export async function sendMessage(body: TSendMessagePayload) {
  try {
    const url = `${API_URL}/api/messages/sendMessage`;
    const token = await AsyncStorage.getItem(TOKEN_KEY);
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    const response = await axios.request({
      url: url,
      data: body,
      method: 'post',
    });
    if (response.data.success) {
      return response?.data?.data;
    }
  } catch (error) {
    console.error('Error sendMessage', error);
  }
}

export async function readMessage(body: { conversation_id: string }) {
  try {
    const url = `${API_URL}/api/messages/readMessage`;
    const token = await AsyncStorage.getItem(TOKEN_KEY);
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    const response = await axios.request({
      url: url,
      data: body,
      method: 'put',
    });
    if (response.data.success) {
      return response?.data?.data;
    }
  } catch (error) {
    console.error('Error readMessage', error);
  }
}

export async function deleteConversation(body: { id: string }) {
  try {
    const url = `${API_URL}/api/messages/deleteConversation`;
    const token = await AsyncStorage.getItem(TOKEN_KEY);
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    const response = await axios.request({
      url: url,
      data: body,
      method: 'delete',
    });
    if (response.data.success) {
      return response?.data?.data;
    }
  } catch (error) {
    console.error('Error deleteConversation', error);
  }
}
