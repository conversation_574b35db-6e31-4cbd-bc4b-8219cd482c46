import axios from 'axios';
import { useState, useMemo, useEffect, useCallback } from 'react';
import { API_URL, useAuth } from '../../context/AuthContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
const TOKEN_KEY = 'my-jwt';

export const useListReviews = (userId) => {
  const [isFetching, setIsFetching] = useState(false);
  const [list, setList] = useState([]);
  const getList = async () => {
    setIsFetching(true);
    try {
      const token = await AsyncStorage.getItem(TOKEN_KEY);
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      if (token) {
        const url = `${API_URL}/api/reviews?userId=${userId}`;
        const response = await axios.request({
          url: url,
          method: 'get',
        });
        if (response?.data?.data?.code) {
          setIsFetching(false);
          setList([]);
        } else {
          setIsFetching(false);
          setList(response.data.data || []);
        }
      } else {
        setIsFetching(false);
        return { error: true, msg: 'Could not get token' };
      }
    } catch (err) {
      setIsFetching(false);
      console.log(err);
    }
  };
  useEffect(() => {
    getList();
  }, []);

  return { isFetching, list, reFetch: getList };
};

export const useCreateReview = () => {
  const [isCreating, setIsCreating] = useState(false);
  const createReview = async (data) => {
    setIsCreating(true);
    try {
      const token = await AsyncStorage.getItem(TOKEN_KEY);
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      if (token) {
        const url = `${API_URL}/api/reviews`;
        const response = await axios.request({
          url: url,
          method: 'post',
          data: data,
        });
        console.log('useCreateReview', response.data);
        if (response?.data?.code) {
          setIsCreating(false);
          return response.data;
        } else {
          setIsCreating(false);
          return response.data || [];
        }
      } else {
        setIsCreating(false);
        return { error: true, msg: 'Could not get token' };
      }
    } catch (err) {
      console.log(err);
    }
  };

  return { isCreating, createReview };
};

export async function readReview(body) {
  try {
    const url = `${API_URL}/api/reviews/readReviews`;
    const token = await AsyncStorage.getItem(TOKEN_KEY);
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    const response = await axios.request({
      url: url,
      data: body,
      method: 'put',
    });
    if (response.data.success) {
      return response?.data?.data;
    }
  } catch (error) {
    console.error('Error postData', error);
  }
}
