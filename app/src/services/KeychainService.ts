import * as Keychain from 'react-native-keychain';

export const CREDENTIALS_SERVICE = 'com.trademotion.credentials';

class KeychainService {
  async storeCredentials(email: string, password: string): Promise<boolean> {
    try {
      console.log('Storing credentials for:', email);
      const result = await Keychain.setGenericPassword(email, password, {
        service: CREDENTIALS_SERVICE,
        accessible: Keychain.ACCESSIBLE.WHEN_UNLOCKED,
      });
      console.log('Credentials stored successfully:', !!result);
      return !!result;
    } catch (error) {
      console.error('Error storing credentials:', error);
      return false;
    }
  }

  async getCredentials(): Promise<{ username: string; password: string } | null> {
    try {
      const credentials = await Keychain.getGenericPassword({
        service: CREDENTIALS_SERVICE,
      });
      console.log('Retrieved credentials:', credentials ? 'found' : 'not found');
      return credentials ? credentials : null;
    } catch (error) {
      console.error('Error retrieving credentials:', error);
      return null;
    }
  }

  async hasCredentials(): Promise<boolean> {
    try {
      const credentials = await this.getCredentials();
      return !!credentials;
    } catch (error) {
      console.error('Error checking credentials:', error);
      return false;
    }
  }

  async resetCredentials(): Promise<void> {
    try {
      console.log('Resetting credentials');
      await Keychain.resetGenericPassword({ service: CREDENTIALS_SERVICE });
      console.log('Credentials reset completed');
    } catch (error) {
      console.error('Error resetting credentials:', error);
    }
  }
}

export default new KeychainService();
