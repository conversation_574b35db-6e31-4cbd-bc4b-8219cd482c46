import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../context/AuthContext';
import * as geolib from 'geolib';
import axiosInstance from '../utils/axiosManager';

export async function postJobs(data) {
  try {
    const url = '/api/jobs';
    const response = await axiosInstance.post(url, JSON.stringify(data), {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    if (response.data.success) {
      return response.data.data;
    }
  } catch (error) {
    console.error('Error postData', error);
  }
}

export const updateJob = async (id, data) => {
  try {
    const url = `/api/jobs/` + id;
    const res = await axiosInstance.put(url, JSON.stringify(data), {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return res.data;
  } catch (error) {
    console.error('Error updateJob', error);
  }
};

export const getJobById = async (id) => {
  const url = `/api/jobs`;
  const res = await axiosInstance.get(`${url}/${id}`);
  return res.data;
};

export const hideJob = async (jobIds) => {
  const url = `/api/jobs/hideJob`;
  const res = await axiosInstance.post(url, JSON.stringify({ jobIds }), {
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return res.data;
};

export const getFavoriteJobList = async () => {
  const url = `/api/jobs/getFavoriteJob`;
  const res = await axiosInstance.get(url);
  return res.data;
};

export const useGetFavoriteJobList = () => {
  const [isFetching, setIsFetching] = useState(false);
  const [list, setList] = useState([]);

  const _getFavoriteJobList = async () => {
    setIsFetching(true);
    try {
      getFavoriteJobList().then((res) => {
        if (res) {
          setList(res.data);
        }
        setIsFetching(false);
      });
    } catch (err) {
      console.error({ useGetFavoriteJobList: err });
    }
  };

  useEffect(() => {
    _getFavoriteJobList();
  }, []);

  return { isFetching, list, refetch: _getFavoriteJobList };
};

export const setFavoriteJob = async (jobId, jobData = null) => {
  const url = `/api/jobs/setFavoriteJob`;
  const payload = { jobId };

  // If jobData is provided, include it to avoid backend queries
  if (jobData) {
    payload.jobData = jobData;
  }

  const res = await axiosInstance.post(url, JSON.stringify(payload), {
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return res.data;
};

export const setHiddenJob = async (jobId, action = null) => {
  const url = `/api/jobs/setHiddenJob`;
  const payload = { jobId };

  // action can be 'hide' or 'unhide', if not provided it will toggle
  if (action) {
    payload.action = action;
  }

  const res = await axiosInstance.post(url, JSON.stringify(payload), {
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return res.data;
};

export const getHiddenJobList = async () => {
  const url = `/api/jobs/setHiddenJob`;
  const res = await axiosInstance.get(url);
  return res.data;
};

export const getHomeGridButtonNumber = async () => {
  const url = `/api/homeScreen`;
  const data = await axiosInstance.get(`${url}`);
  return data.data;
};

export const useGetJobDetail = (id) => {
  const [isFetching, setIsFetching] = useState(false);
  const [data, setData] = useState(null);

  const getJobDetail = async () => {
    setIsFetching(true);
    try {
      let result = await getJobById(id);
      setData(result.data);
    } catch (err) {
      console.log(err);
    }
    setIsFetching(false);
  };

  useEffect(() => {
    getJobDetail();
  }, []);

  return { isFetching, data, reFetch: getJobDetail };
};

export const useListBusinessCurrentPosts = () => {
  const { authState } = useAuth();
  const [isFetching, setIsFetching] = useState(false);
  const [list, setList] = useState([]);

  const getList = async () => {
    setIsFetching(true);
    try {
      const res = await axiosInstance.get(`/api/jobs?user_post=${encodeURIComponent(authState.user.id)}`);
      setList(Array.isArray(res.data?.data) ? res.data.data : []);
    } catch (err) {
      setIsFetching(false);
      setList([]);
    }
    setIsFetching(false);
  };

  useEffect(() => {
    getList();
  }, []);

  return { isFetching, list, reFetch: getList };
};

export const useListTradespersonJobs = (query) => {
  const [isFetching, setIsFetching] = useState(false);
  const [list, setList] = useState([]);

  const getList = async () => {
    setIsFetching(true);
    try {
      const params = new URLSearchParams(query).toString();
      const url = `/api/jobs/getJobs?${params}`;
      const response = await axiosInstance.request({
        url: url,
        method: 'get',
      });
      console.log('response', response.data);
      if (response?.data?.data?.code) {
        setList([]);
      } else {
        //log location
        console.log('query location', query?.location);
        const listData = response.data.data || [];
        if (query.searchRadius !== '0') {
          if (query.searchRadius === '50+') {
            const filteredList = listData.filter((item) => {
              if (!query?.location?.latitude || !query?.location?.longitude) {
                return true;
              }
              if (!item?.location_lat || !item?.location_long) {
                return false;
              }
              if (item?.location_lat === 0 && item?.location_long === 0) {
                return false;
              }
              const distance = geolib.getDistance(
                { latitude: query?.location?.latitude, longitude: query?.location?.longitude },
                { latitude: item?.location_lat || 0, longitude: item?.location_long || 0 }
              );
              return distance > 50 * 1609.34;
            });
            setList(filteredList);
          } else {
            const filteredList = listData.filter((item) => {
              if (!query?.location?.latitude || !query?.location?.longitude) {
                return true;
              }
              if (!item?.location_lat || !item?.location_long) {
                return false;
              }
              if (item?.location_lat === 0 && item?.location_long === 0) {
                return false;
              }
              const distance = geolib.getDistance(
                { latitude: query?.location?.latitude, longitude: query?.location?.longitude },
                { latitude: item?.location_lat || 0, longitude: item?.location_long || 0 }
              );
              return distance <= query.searchRadius * 1609.34;
            });
            setList(filteredList);
          }
        } else {
          setList(listData);
        }
      }
    } catch (err) {
      console.log(err);
    }
    setIsFetching(false);
  };
  useEffect(() => {
    getList();
  }, []);

  return { isFetching, list, reFetch: getList };
};

export const applyJob = async (jobId, userData, businessId) => {
  const url = `/api/jobApplication`;
  const res = await axiosInstance.post(
    url,
    JSON.stringify({
      tradesperson_email: userData.email,
      title: userData.email,
      tradesperson: userData.id,
      job: jobId,
      job_status: 'Pending',
      business: [businessId],
    }),
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
  return res.data;
};

export const useListTradespersonApplications = () => {
  const { authState } = useAuth();
  const [isFetching, setIsFetching] = useState(false);
  const [list, setList] = useState([]);

  const reFetch = useCallback(async () => {
    setIsFetching(true);
    try {
      const res = await axiosInstance.get(`/api/jobApplication/getJobs?tradesperson_email=${encodeURIComponent(authState.user.email)}`);
      setList(res.data.data || []);
    } catch (err) {}

    setIsFetching(false);
  }, []);

  useEffect(() => {
    reFetch();
  }, []);

  return { isFetching, list, reFetch: reFetch };
};

export const useListBusinessApplicationsOfAJob = (jobId) => {
  const [list, setList] = useState([]);
  const [isFetching, setIsFetching] = useState(false);

  const getList = async () => {
    setIsFetching(true);
    try {
      const response = await axiosInstance.get(`/api/jobs/${jobId}/applications`);
      setList(response.data.data || []);
    } catch (error) {
      console.error(error);
    } finally {
      setIsFetching(false);
    }
  };

  const reFetch = () => {
    getList();
  };

  useEffect(() => {
    getList();
  }, [jobId]);

  return { list, isFetching, reFetch };
};

export const businessApproveJob = async ({ jobId, applicationApproveId }) => {
  const url = `/api/jobs/approve`;
  const res = await axiosInstance.post(url, { jobId, applicationApproveId });
  return res.data;
};

export const hideExpiredJobsApplication = async (jobIds) => {
  const url = `/api/jobApplication/hideExpiredJobs`;
  const res = await axiosInstance.put(url, { jobsId: jobIds });
  return res.data;
};

export const hideCompletedJobs = async (jobIds) => {
  const url = `/api/jobs/hideCompletedJobs`;
  const res = await axiosInstance.put(url, { jobsId: jobIds });
  return res.data;
};
