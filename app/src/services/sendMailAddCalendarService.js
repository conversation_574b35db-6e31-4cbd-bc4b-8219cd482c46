import axios from 'axios';
import {authCons} from '../constants/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {API_URL} from '../../context/AuthContext';
const TOKEN_KEY = 'my-jwt';

export async function sendMailAddCalendar(data) {
  try {
    const url = `${API_URL}/api/add-to-calendar`;
    const token = await AsyncStorage.getItem(TOKEN_KEY);
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    const response = await axios.request({
      url: url,
      data: data,
      method: 'post',
    });
    if (response.data.success) {
      return response.data.success;
    }
  } catch (error) {
    console.error('Error postData', error);
  }
}
