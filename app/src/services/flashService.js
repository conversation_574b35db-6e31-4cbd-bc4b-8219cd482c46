import axios from 'axios';
import { authCons } from '../constants/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_URL } from '../../context/AuthContext';
import { useState, useEffect } from 'react';
import { Alert } from 'react-native';

const TOKEN_KEY = 'my-jwt';

export async function createFlash(body) {
  try {
    const url = `${API_URL}/api/flash`;
    const token = await AsyncStorage.getItem(TOKEN_KEY);
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    const response = await axios.request({
      url: url,
      data: body,
      method: 'post',
    });
    if (response.data.success) {
      return response.data.data;
    } else if (response.data.success === false && response.data.code === 403) {
      return {
        success: false,
        code: 403,
      };
    }
  } catch (error) {
    console.error('Error postData', error);
  }
}

export async function updateFlash(body) {
  try {
    const url = `${API_URL}/api/flash/${body.poster}`;
    const token = await AsyncStorage.getItem(TOKEN_KEY);
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    const response = await axios.request({
      url: url,
      data: body,
      method: 'put',
    });
    if (response.data.success) {
      return response.data.data;
    } else if (response.data.success === false && response.data.code === 403) {
      return {
        success: false,
        code: 403,
      };
    }
  } catch (error) {
    console.error('Error postData', error);
  }
}

export async function getFlash() {
  try {
    const url = `${API_URL}/api/flash/getList`;
    const token = await AsyncStorage.getItem(TOKEN_KEY);
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    const response = await axios.request({
      url: url,
      method: 'get',
    });

    if (response.data.success) {
      return response.data.data;
    } else {
      console.error(response);
    }
  } catch (error) {
    console.error('Error postData', error);
  }
}

export async function getFlashReadStatus() {
  try {
    const url = `${API_URL}/api/flash/getReadStatusList`;
    const token = await AsyncStorage.getItem(TOKEN_KEY);
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    const response = await axios.request({
      url: url,
      method: 'get',
    });
    if (response.data.success) {
      return response.data.data;
    } else {
      console.error(response);
    }
  } catch (error) {
    console.error('Error on getFlashReadStatus', error);
  }
}

export async function setReadFlash(flashId) {
  try {
    const url = `${API_URL}/api/flash/readFlash`;
    const token = await AsyncStorage.getItem(TOKEN_KEY);
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    const response = await axios.request({
      url: url,
      method: 'put',
      data: {
        flash_id: flashId,
      },
    });
    console.log({ setReadFlash: response });
    if (response.data.success) {
      return response.data.data;
    }
  } catch (error) {
    console.error('Error on setReadFlash', error);
  }
}

export async function getFlashById(flashId) {
  try {
    const url = `${API_URL}/api/flash/${flashId}`;
    const token = await AsyncStorage.getItem(TOKEN_KEY);
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    const response = await axios.request({
      url: url,
      method: 'get',
    });
    if (response.data.success) {
      return response.data.data;
    } else if (response.data.success === false && response.data.code === 403) {
      return {
        success: false,
        code: 403,
      };
    }
  } catch (error) {
    console.error('Error postData', error);
  }
}

export const useListBusinessGetFlash = (userId) => {
  const [list, setList] = useState([]);
  const [isFetching, setIsFetching] = useState(false);

  const getList = async () => {
    setIsFetching(true);
    try {
      const url = `${API_URL}/api/flash?poster=${userId}`;
      const response = await axios.request({
        url: url,
        method: 'get',
      });
      setList(response.data.data || []);
    } catch (error) {
      console.error('getList', error);
    } finally {
      setIsFetching(false);
    }
  };

  const reFetch = () => {
    getList();
  };

  useEffect(() => {
    getList();
  }, [userId]);

  return { list, isFetching, reFetch };
};
