import { Alert } from 'react-native';
import moment from 'moment-timezone';

export const utils = {
  handleErrorNotify: (error) => {
    let message = typeof error === 'string' ? error : error.response?.data?.message || 'Something went wrong';

    Alert.alert('Error', message, [{ text: 'Ok' }]);
  },
};

export const fromNow = (time, timeFormat) => {
  return moment.tz(time, timeFormat, 'Europe/London').fromNow();
};
