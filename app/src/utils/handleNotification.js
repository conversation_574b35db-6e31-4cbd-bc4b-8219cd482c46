export const handleNotification = (event, navigate) => {
  if (event?.notification) {
    const screen = event.notification?.rawPayload?.custom?.a?.schemaLink?.screen;
    const params = event.notification?.rawPayload?.custom?.a?.schemaLink?.params;
    if (screen) {
      navigate('Home', { screen, params });
    }
  } else if (event?.payload) {
    const screen = event.payload.rawPayload?.custom?.a?.schemaLink?.screen;
    const params = event.payload.rawPayload?.custom?.a?.schemaLink?.params;
    if (screen) {
      navigate('Home', { screen, params });
    }
  }
};
