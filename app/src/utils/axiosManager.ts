import axios from 'axios';
import { Alert } from 'react-native';
import { API_URL } from '../../context/AuthContext';
import { navigationRef } from './navigationRef';

// Create axios instance with default config
const axiosInstance = axios.create({
  baseURL: API_URL,
  timeout: 0,
});

// Request interceptor for adding auth token
axiosInstance.interceptors.request.use(
  (config) => {
    // Get token from storage or context
    const token = global.token;
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
axiosInstance.interceptors.response.use(
  (response) => {
    // Handle responses with error codes from your API
    if (response.data.code) {
      if (response.data.code === 403) {
        Alert.alert('Session Expired', 'Your session has expired. Please login again.', [
          {
            text: 'OK',
            onPress: () => {
              navigationRef.current?.reset({
                index: 0,
                routes: [{ name: 'Login' }],
              });
            },
          },
        ]);
        return Promise.reject(response.data);
      }
      return Promise.reject(response.data);
    }
    return response;
  },
  (error) => {
    if (error.response?.status === 403) {
      Alert.alert('Session Expired', 'Your session has expired. Please login again.', [
        {
          text: 'OK',
          onPress: () => {
            navigationRef.current?.reset({
              index: 0,
              routes: [{ name: 'Login' }],
            });
          },
        },
      ]);
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;
