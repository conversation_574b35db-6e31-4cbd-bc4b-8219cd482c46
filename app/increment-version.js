const fs = require('fs');
const path = require('path');

const packagePath = path.resolve(__dirname, 'package.json');
const packageJson = require(packagePath);

const versionParts = packageJson.version.split('.');
const patchVersion = parseInt(versionParts[2]);
versionParts[2] = (patchVersion + 1).toString();

packageJson.version = versionParts.join('.');

fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2) + '\n');
