# image: node:18

stages:
  - code-push

before_script:
  - nvm install 18 && nvm use 18
  - npm install -g yarn appcenter-cli
  - yarn install
code-push-dev:
  stage: code-push
  tags:
    - shell
  script:
    - appcenter login --token $APPCENTER_TOKEN
    - appcenter codepush release-react -a lequanghuylc/Trade-Motion-iOS --target-binary-version "1.6.0"
    - appcenter logout
  only:
    - dev

code-push-testflight:
  stage: code-push
  tags:
    - shell
  script:
    - appcenter login --token $APPCENTER_TOKEN
    - appcenter codepush release-react -a lequanghuylc/Trade-Motion-iOS --target-binary-version "1.12.1"
    - appcenter logout
  only:
    - main

code-push-production:
  stage: code-push
  tags:
    - shell
  script:
    - appcenter login --token $APPCENTER_TOKEN
    - appcenter codepush release-react -a lequanghuylc/Trade-Motion-iOS --target-binary-version "1.0.5"
    - appcenter logout
  only:
    - prod

