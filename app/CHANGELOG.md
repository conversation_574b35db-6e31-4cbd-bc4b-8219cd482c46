# Changelog

All notable changes to this project will be documented in this file. See
[standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [0.0.7](https://gitlab.personify.tech/Daniel.Cheung/trademotion/compare/v0.0.5...v0.0.7) (2024-04-24)

### Bug Fixes

- fix bugs feed back from tester ([a073751](https://gitlab.personify.tech/Daniel.Cheung/trademotion/commit/a0737514bd4630d1083a98ef9245281150775926))
- fix conflict main-not-test-figlt with dev
  ([2e826f1](https://gitlab.personify.tech/Daniel.Cheung/trademotion/commit/2e826f1007976b46bf0408b5fead7b33eefe5f43))
- fix message have long text, read and unread message, Postjob cash
  ([c760820](https://gitlab.personify.tech/<PERSON>.Cheung/trademotion/commit/c760820312e006896cbb254823af026173963626))
- login switch, Flash detail, filter job, add reminder, change color icon home
  ([4e19020](https://gitlab.personify.tech/<PERSON>.<PERSON>/trademotion/commit/4e19020a3e5e241f5bd068e0c3b567432cb1b9b9))

### [0.0.6](https://gitlab.personify.tech/Daniel.Cheung/trademotion/compare/v0.0.5...v0.0.6) (2024-04-24)

### Bug Fixes

- fix bugs feed back from tester ([a073751](https://gitlab.personify.tech/Daniel.Cheung/trademotion/commit/a0737514bd4630d1083a98ef9245281150775926))
- fix conflict main-not-test-figlt with dev
  ([2e826f1](https://gitlab.personify.tech/Daniel.Cheung/trademotion/commit/2e826f1007976b46bf0408b5fead7b33eefe5f43))
- fix message have long text, read and unread message, Postjob cash
  ([c760820](https://gitlab.personify.tech/Daniel.Cheung/trademotion/commit/c760820312e006896cbb254823af026173963626))
- login switch, Flash detail, filter job, add reminder, change color icon home
  ([4e19020](https://gitlab.personify.tech/Daniel.Cheung/trademotion/commit/4e19020a3e5e241f5bd068e0c3b567432cb1b9b9))

### 0.0.5 (2024-04-19)

### Features

- add chat app ([ae820ec](https://gitlab.personify.tech/Daniel.Cheung/trademotion/commit/ae820ec0aba4653a318f68d372ff71b08cf84d1b))
- add create new mess from find jobs
  ([b377f5f](https://gitlab.personify.tech/Daniel.Cheung/trademotion/commit/b377f5facde7247031c8d2429f8d516d65e4760c))
- register and jobs ([e01b668](https://gitlab.personify.tech/Daniel.Cheung/trademotion/commit/e01b668793ac64ce286456de779f0fdfa0fa90f7))

### Bug Fixes

- fix bugs ([2abfd1e](https://gitlab.personify.tech/Daniel.Cheung/trademotion/commit/2abfd1efd7ae920480b11227656871634779c9a1))
- fix plus version when commit ([7c5a73e](https://gitlab.personify.tech/Daniel.Cheung/trademotion/commit/7c5a73e3c9700852922341a2b57c08193766310c))
- handle notification ([e081cb2](https://gitlab.personify.tech/Daniel.Cheung/trademotion/commit/e081cb215f35de279c3e822ea27a9531eb045971))
