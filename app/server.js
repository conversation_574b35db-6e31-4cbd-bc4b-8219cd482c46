const express = require('express');
const app = express();
const port = 5001;

// create an array to store articles
let pendingJob = [
  {
    id: 1,
    name: 'Google',
    title: 'Tradesman',
    status: 'Status: ',
  },
  {
    id: 2,
    name: 'Company name',
    title: 'Tradesman',
    status: 'Status: ',
  },
];

let messages = [
  {
    id: 1,
    name: '<PERSON>',
    message: 'Lorem ipsum de...',
    time: '12:23am',
  },
  {
    id: 2,
    name: '<PERSON>',
    message: 'Lorem ipsum de...',
    time: '12:23am',
  },
];

let newsFlash = [
  {
    id: 1,
    name: 'Company name',
    news: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor',
  },
];

app.get('/', (req, res) => {
  res.send('Hello World!');
});

app.get('/pendingJob', (req, res) => {
  res.json({data: pendingJob});
});

app.get('/messages', (req, res) => {
  res.json({data: messages});
});

app.get('/newsFlash', (req, res) => {
  res.json({data: newsFlash});
});

app.post('/authentication', (req, res) => {
  res.json({data: pendingJob});
});

app.listen(port, () =>
  console.log(`API listening at http://localhost:${port}`),
);
