import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import Login from './src/screens/Login';
import Register from './src/components/Register';
import Home from './src/screens/Home';
import TestMap from './src/screens/TestMap';
import Profile from './src/components/Profile';
import ProfileEdit from './src/components/ProfileEdit';
import Messages from './src/components/Messages';
import Reviews from './src/components/Reviews';
import FindJob from './src/components/FindJob';
import Newsflash from './src/components/Newsflash';
import Applications from './src/screens/TradespersonApplications';
import PostJob from './src/screens/PostJob';
import CurrentPost from './src/components/CurrentPost';
import Payment from './src/components/Payment';
import BusinessMyJobDetail from './src/screens/BusinessMyJobDetail';
import BusinessApplicationsOfAJob from './src/screens/BusinessApplicationsOfAJob';
import ComingSoon from './src/components/ComingSoon';
import Chat from './src/screens/Chat';
import MapViewJob from './src/components/MapViewJob';
import NewsflashDetail from './src/components/NewsflashDetail';
import BusinessListFlash from './src/screens/BusinessListFlash';
import PostNewsFlash from './src/screens/PostNewsFlash';
import BusinessMyNewsFlashDetail from './src/screens/BusinessMyNewsFlashDetail';
import ApplyJobDetail from './src/components/ApplyJobDetail';
import BookedJobs from './src/screens/BookedJobs';
import Portfolio from './src/components/Portfolio';
import ApprovedJobDetail from './src/screens/ApprovedJobDetail';

import { GestureHandlerRootView } from 'react-native-gesture-handler';
import RegisterInvoiceDetails from './src/components/RegisterInvoiceDetails';
import AssignNumberOrder from './src/screens/AssignNumberOrder';

const Stack = createNativeStackNavigator();

export const Layout = () => {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
        }}
      >
        {/* <Stack.Screen name="Index" component={Index} /> */}
        <Stack.Screen name="Login" component={Login} />
        <Stack.Screen name="Register" component={Register} />
        <Stack.Screen name="RegisterInvoiceDetails" component={RegisterInvoiceDetails} />
        <Stack.Screen name="Home" component={Home} />
        <Stack.Screen name="Profile" component={Profile} />
        <Stack.Screen name="Portfolio" component={Portfolio} />
        <Stack.Screen name="ProfileEdit" component={ProfileEdit} />
        <Stack.Screen name="Messages" component={Messages} />
        <Stack.Screen name="Reviews" component={Reviews} />
        <Stack.Screen name="FindJob" component={FindJob} />
        <Stack.Screen name="Newsflash" component={Newsflash} />
        <Stack.Screen name="Applications" component={Applications} />
        <Stack.Screen name="PostJob" component={PostJob} />
        <Stack.Screen name="CurrentPost" component={CurrentPost} />
        <Stack.Screen name="Payment" component={Payment} />
        <Stack.Screen name="TestMap" component={TestMap} />
        <Stack.Screen name="BusinessMyJobDetail" component={BusinessMyJobDetail} />
        <Stack.Screen name="BusinessApplicationsOfAJob" component={BusinessApplicationsOfAJob} />
        <Stack.Screen name="ComingSoon" component={ComingSoon} />
        <Stack.Screen name="Chat" component={Chat} />
        <Stack.Screen name="MapViewJob" component={MapViewJob} />
        <Stack.Screen name="NewsflashDetail" component={NewsflashDetail} />
        <Stack.Screen name="FlashBusiness" component={BusinessListFlash} />
        <Stack.Screen name="PostNewsFlash" component={PostNewsFlash} />
        <Stack.Screen name="BusinessMyNewsFlashDetail" component={BusinessMyNewsFlashDetail} />
        <Stack.Screen name="ApplyJobDetail" component={ApplyJobDetail} />
        <Stack.Screen name="AssignNumberOrder" component={AssignNumberOrder} />
        <Stack.Screen name="BookedJobs" component={BookedJobs} />
        <Stack.Screen name="ApprovedJobDetail" component={ApprovedJobDetail} />
      </Stack.Navigator>
    </GestureHandlerRootView>
  );
};
