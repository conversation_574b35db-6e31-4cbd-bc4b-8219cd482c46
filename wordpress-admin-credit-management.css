/* 
Title: Credit Management CSS
Code Type: CSS Stylesheet
Only run in administration area: YES (tick checkbox)
*/

#credit-management-section {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    margin: 20px 0;
    padding: 0;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

#credit-management-section .postbox-header {
    background: linear-gradient(135deg, #f6f7f7 0%, #eaeaea 100%);
    border-bottom: 1px solid #c3c4c7;
    padding: 12px 20px;
    margin: 0;
}

#credit-management-section .postbox-header h2 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1d2327;
    text-shadow: 0 1px 0 rgba(255,255,255,0.8);
}

#credit-management-section .inside {
    padding: 20px;
}

.credit-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

@media (max-width: 768px) {
    .credit-info-grid {
        grid-template-columns: 1fr;
    }
}

.credit-balance-display {
    background: linear-gradient(135deg, #f0f6fc 0%, #e7f3ff 100%);
    border: 2px solid #2271b1;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.credit-balance-display::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.credit-balance-display h3 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #646970;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.credit-balance-number {
    font-size: 36px;
    font-weight: bold;
    color: #2271b1;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: relative;
    z-index: 1;
}

.credit-adjust-form {
    background: #f9f9f9;
    border: 1px solid #c3c4c7;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s ease;
}

.credit-adjust-form:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.credit-adjust-form h4 {
    margin: 0 0 15px 0;
    font-size: 14px;
    color: #1d2327;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.credit-adjust-form input {
    width: 100%;
    margin-bottom: 12px;
    padding: 12px 15px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    box-sizing: border-box;
    transition: all 0.3s ease;
}

.credit-adjust-form input:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 3px rgba(34, 113, 177, 0.1);
    outline: none;
    transform: translateY(-1px);
}

.credit-adjust-form input[type="number"] {
    font-weight: bold;
    font-size: 16px;
}

.credit-adjust-form button {
    width: 100%;
    padding: 12px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.credit-adjust-form button:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(34, 113, 177, 0.3);
}

.credit-adjust-form button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.credit-history {
    margin-top: 25px;
}

.credit-history h4 {
    margin: 0 0 15px 0;
    font-size: 14px;
    color: #1d2327;
    border-bottom: 2px solid #2271b1;
    padding-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.transaction-item {
    border-bottom: 1px solid #f0f0f1;
    padding: 15px 0;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    transition: all 0.3s ease;
}

.transaction-item:hover {
    background: rgba(34, 113, 177, 0.02);
    padding-left: 10px;
    padding-right: 10px;
    margin-left: -10px;
    margin-right: -10px;
    border-radius: 6px;
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-details {
    flex: 1;
    margin-right: 15px;
}

.transaction-type {
    font-weight: 600;
    text-transform: capitalize;
    color: #1d2327;
    margin-bottom: 4px;
    font-size: 15px;
}

.transaction-amount {
    font-weight: bold;
    font-size: 16px;
    white-space: nowrap;
    padding: 4px 8px;
    border-radius: 4px;
    text-align: center;
    min-width: 80px;
}

.transaction-amount.positive {
    color: #00a32a;
    background: rgba(0, 163, 42, 0.1);
    border: 1px solid rgba(0, 163, 42, 0.2);
}

.transaction-amount.negative {
    color: #d63638;
    background: rgba(214, 54, 56, 0.1);
    border: 1px solid rgba(214, 54, 56, 0.2);
}

.transaction-meta {
    font-size: 12px;
    color: #646970;
    line-height: 1.5;
}

.loading {
    text-align: center;
    padding: 30px;
    color: #646970;
    font-style: italic;
    background: rgba(0,0,0,0.02);
    border-radius: 6px;
}

.error {
    background: linear-gradient(135deg, #fcf2f2 0%, #fdf2f2 100%);
    border-left: 4px solid #d63638;
    color: #d63638;
    padding: 12px 16px;
    border-radius: 0 6px 6px 0;
    margin: 10px 0;
    box-shadow: 0 2px 4px rgba(214, 54, 56, 0.1);
}

.success {
    background: linear-gradient(135deg, #f0f6fc 0%, #e7f3ff 100%);
    border-left: 4px solid #00a32a;
    color: #00a32a;
    padding: 12px 16px;
    border-radius: 0 6px 6px 0;
    margin: 10px 0;
    box-shadow: 0 2px 4px rgba(0, 163, 42, 0.1);
}

.info {
    background: linear-gradient(135deg, #f0f6fc 0%, #e7f3ff 100%);
    border-left: 4px solid #72aee6;
    color: #2c3338;
    padding: 12px 16px;
    border-radius: 0 6px 6px 0;
    margin: 10px 0;
    box-shadow: 0 2px 4px rgba(114, 174, 230, 0.1);
}

/* Animation for loading spinner */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.spin {
    animation: spin 1s linear infinite;
}

/* Responsive adjustments */
@media (max-width: 600px) {
    .transaction-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .transaction-details {
        margin-right: 0;
        margin-bottom: 8px;
    }
    
    .transaction-amount {
        align-self: flex-end;
    }
}

/* Enhanced button states */
.button-primary:not(:disabled) {
    background: linear-gradient(135deg, #2271b1 0%, #135e96 100%);
    border-color: #135e96;
    text-shadow: 0 1px 0 rgba(0,0,0,0.2);
}

.button-primary:not(:disabled):hover {
    background: linear-gradient(135deg, #135e96 0%, #0f4c7c 100%);
    border-color: #0f4c7c;
}

/* Pulse animation for balance display */
.credit-balance-number {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

/* Smooth transitions for all interactive elements */
* {
    transition: all 0.3s ease;
}
